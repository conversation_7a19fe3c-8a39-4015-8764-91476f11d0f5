// e2e/tests/basic-functionality-test.spec.ts
import { test, expect, Page } from '@playwright/test';

const FRONTEND_URL = 'http://localhost:5001';

async function loginUser(page: Page) {
  await page.goto(FRONTEND_URL);
  await page.waitForSelector('form', { timeout: 10000 });
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'testpass123');
  await page.click('[data-testid="login-button"]');
  await page.waitForSelector('[data-testid="dashboard-header"]', { timeout: 15000 });
}

test.describe('Basic Functionality Tests', () => {
  
  test('Test Case 1: Login and Dashboard Access', async ({ page }) => {
    console.log('Starting Test Case 1: Login and Dashboard Access');
    
    // Step 1: Login
    await loginUser(page);
    console.log('✅ User logged in successfully');
    
    // Step 2: Verify dashboard elements are visible
    await expect(page.locator('[data-testid="dashboard-header"]')).toBeVisible();
    await expect(page.locator('[data-testid="conversation-list"]')).toBeVisible();
    await expect(page.locator('[data-testid="chat-area"]')).toBeVisible();
    await expect(page.locator('[data-testid="new-chat-button"]')).toBeVisible();
    console.log('✅ Dashboard elements are visible');
    
    // Step 3: Check connection status
    const connectionStatus = await page.locator('[data-testid="connection-status"]');
    await expect(connectionStatus).toBeVisible();
    const statusText = await connectionStatus.textContent();
    console.log(`Connection status: ${statusText}`);
    
    console.log('✅ Test Case 1 completed successfully');
  });

  test('Test Case 2: User Search Functionality', async ({ page }) => {
    console.log('Starting Test Case 2: User Search Functionality');
    
    // Step 1: Login
    await loginUser(page);
    console.log('✅ User logged in successfully');
    
    // Step 2: Open user search modal
    await page.click('[data-testid="new-chat-button"]');
    await page.waitForSelector('[data-testid="user-search-modal"]', { timeout: 10000 });
    console.log('✅ User search modal opened');
    
    // Step 3: Search for harry
    await page.fill('[data-testid="user-search-input"]', 'harry');
    console.log('✅ Search query entered');
    
    // Step 4: Wait for search results
    await page.waitForTimeout(3000); // Wait for debounced search
    
    // Step 5: Check if user results appear
    const userResults = await page.locator('[data-testid="user-result"]').count();
    console.log(`Found ${userResults} user results`);
    expect(userResults).toBeGreaterThan(0);
    
    // Step 6: Verify user information is displayed
    const userResult = page.locator('[data-testid="user-result"]').first();
    await expect(userResult).toBeVisible();
    await expect(userResult.locator('[data-testid="user-action-button"]')).toBeVisible();
    console.log('✅ User search results are displayed correctly');
    
    console.log('✅ Test Case 2 completed successfully');
  });

  test('Test Case 3: Chat Creation', async ({ page }) => {
    console.log('Starting Test Case 3: Chat Creation');
    
    // Step 1: Login
    await loginUser(page);
    console.log('✅ User logged in successfully');
    
    // Step 2: Create chat with harry
    await page.click('[data-testid="new-chat-button"]');
    await page.waitForSelector('[data-testid="user-search-modal"]', { timeout: 10000 });
    await page.fill('[data-testid="user-search-input"]', 'harry');
    await page.waitForTimeout(3000); // Wait for debounced search
    
    // Step 3: Click on user to create chat
    await page.click('[data-testid="user-action-button"]');
    console.log('✅ User action button clicked');
    
    // Step 4: Verify chat area is visible
    await page.waitForSelector('[data-testid="chat-area"]', { timeout: 10000 });
    console.log('✅ Chat area is visible');
    
    // Step 5: Verify message input exists (even if disabled)
    const messageInput = page.locator('[data-testid="message-input"]');
    await expect(messageInput).toBeVisible();
    console.log('✅ Message input is visible');
    
    // Step 6: Verify send button exists
    const sendButton = page.locator('[data-testid="send-button"]');
    await expect(sendButton).toBeVisible();
    console.log('✅ Send button is visible');
    
    console.log('✅ Test Case 3 completed successfully');
  });

  test('Test Case 4: Page Refresh Persistence', async ({ page }) => {
    console.log('Starting Test Case 4: Page Refresh Persistence');
    
    // Step 1: Login and create chat
    await loginUser(page);
    await page.click('[data-testid="new-chat-button"]');
    await page.waitForSelector('[data-testid="user-search-modal"]', { timeout: 10000 });
    await page.fill('[data-testid="user-search-input"]', 'harry');
    await page.waitForTimeout(3000);
    await page.click('[data-testid="user-action-button"]');
    await page.waitForSelector('[data-testid="chat-area"]', { timeout: 10000 });
    console.log('✅ Chat created');
    
    // Step 2: Refresh the page
    await page.reload();
    await page.waitForSelector('[data-testid="dashboard-header"]', { timeout: 15000 });
    console.log('✅ Page refreshed and dashboard loaded');
    
    // Step 3: Check if conversation list has items (indicating persistence)
    const conversationList = page.locator('[data-testid="conversation-list"]');
    await expect(conversationList).toBeVisible();
    console.log('✅ Conversation list is visible after refresh');
    
    console.log('✅ Test Case 4 completed successfully');
  });

  test('Test Case 5: Network Traffic Analysis', async ({ page }) => {
    console.log('Starting Test Case 5: Network Traffic Analysis');
    
    const networkRequests: any[] = [];
    const responses: any[] = [];
    
    // Monitor all network requests
    page.on('request', (request) => {
      networkRequests.push({
        url: request.url(),
        method: request.method(),
        headers: request.headers(),
        postData: request.postData()
      });
    });
    
    page.on('response', (response) => {
      responses.push({
        url: response.url(),
        status: response.status(),
        headers: response.headers()
      });
    });
    
    // Perform login and basic actions
    await loginUser(page);
    await page.click('[data-testid="new-chat-button"]');
    await page.waitForSelector('[data-testid="user-search-modal"]', { timeout: 10000 });
    await page.fill('[data-testid="user-search-input"]', 'harry');
    await page.waitForTimeout(3000);
    
    // Analyze network traffic
    console.log('Network Requests Analysis:');
    let apiRequests = 0;
    let secureRequests = 0;
    
    networkRequests.forEach((req, index) => {
      if (req.url.includes('/api/')) {
        apiRequests++;
        console.log(`API Request ${apiRequests}: ${req.method} ${req.url}`);
        
        // Check for proper authentication headers
        if (req.headers.authorization) {
          console.log('  ✅ Has authorization header');
        } else {
          console.log('  ⚠️  Missing authorization header');
        }
      }
      
      if (req.url.startsWith('https://')) {
        secureRequests++;
      }
    });
    
    console.log(`Total API requests: ${apiRequests}`);
    console.log(`Total secure requests: ${secureRequests}`);
    
    // Analyze responses
    console.log('Response Analysis:');
    let errorResponses = 0;
    responses.forEach((res) => {
      if (res.status >= 400) {
        errorResponses++;
        console.log(`❌ Error response: ${res.status} ${res.url}`);
      }
    });
    
    console.log(`Total error responses: ${errorResponses}`);
    console.log('✅ Network traffic analysis completed');
  });

  test('Test Case 6: Console Error Analysis', async ({ page }) => {
    console.log('Starting Test Case 6: Console Error Analysis');
    
    const consoleMessages: any[] = [];
    const pageErrors: any[] = [];
    
    // Monitor console messages
    page.on('console', (msg) => {
      consoleMessages.push({
        type: msg.type(),
        text: msg.text(),
        location: msg.location()
      });
    });
    
    // Monitor page errors
    page.on('pageerror', (error) => {
      pageErrors.push({
        message: error.message,
        stack: error.stack
      });
    });
    
    // Perform comprehensive user actions
    await loginUser(page);
    await page.click('[data-testid="new-chat-button"]');
    await page.waitForSelector('[data-testid="user-search-modal"]', { timeout: 10000 });
    await page.fill('[data-testid="user-search-input"]', 'harry');
    await page.waitForTimeout(3000);
    await page.click('[data-testid="user-action-button"]');
    await page.waitForSelector('[data-testid="chat-area"]', { timeout: 10000 });
    
    // Analyze console messages
    console.log('Console Messages Analysis:');
    const errorMessages = consoleMessages.filter(msg => msg.type === 'error');
    const warningMessages = consoleMessages.filter(msg => msg.type === 'warning');
    
    if (errorMessages.length > 0) {
      console.error('❌ Console Errors Found:');
      errorMessages.forEach((error, index) => {
        console.error(`  Error ${index + 1}: ${error.text}`);
      });
    } else {
      console.log('✅ No console errors found');
    }
    
    if (warningMessages.length > 0) {
      console.warn('⚠️  Console Warnings Found:');
      warningMessages.forEach((warning, index) => {
        console.warn(`  Warning ${index + 1}: ${warning.text}`);
      });
    } else {
      console.log('✅ No console warnings found');
    }
    
    // Analyze page errors
    if (pageErrors.length > 0) {
      console.error('❌ Page Errors Found:');
      pageErrors.forEach((error, index) => {
        console.error(`  Page Error ${index + 1}: ${error.message}`);
      });
    } else {
      console.log('✅ No page errors found');
    }
    
    console.log('✅ Console and error analysis completed');
  });
});
