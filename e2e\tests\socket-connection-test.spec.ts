// e2e/tests/socket-connection-test.spec.ts
import { test, expect, Page } from '@playwright/test';

const FRONTEND_URL = 'http://localhost:5001';

async function loginUser(page: Page) {
  await page.goto(FRONTEND_URL);
  await page.waitForSelector('form', { timeout: 10000 });
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'testpass123');
  await page.click('[data-testid="login-button"]');
  await page.waitForSelector('[data-testid="dashboard-header"]', { timeout: 15000 });
}

test.describe('Socket Connection Debugging', () => {
  test('should debug socket connection issues', async ({ page }) => {
    console.log('Starting socket connection debugging');
    
    // Monitor console messages for socket-related logs
    const consoleMessages: string[] = [];
    page.on('console', (msg) => {
      const text = msg.text();
      consoleMessages.push(text);
      if (text.includes('socket') || text.includes('Socket') || text.includes('connect') || text.includes('Connect')) {
        console.log(`Console: ${text}`);
      }
    });
    
    // Monitor network requests for socket.io
    const networkRequests: any[] = [];
    page.on('request', (request) => {
      const url = request.url();
      if (url.includes('socket.io') || url.includes('7000')) {
        networkRequests.push({
          url: url,
          method: request.method(),
          headers: request.headers()
        });
        console.log(`Network Request: ${request.method()} ${url}`);
      }
    });
    
    // Login
    await loginUser(page);
    console.log('✅ Logged in successfully');
    
    // Wait for socket connection attempts
    await page.waitForTimeout(10000);
    
    // Check connection status
    const connectionStatus = await page.locator('[data-testid="connection-status"]');
    const statusText = await connectionStatus.textContent();
    console.log(`Connection status: ${statusText}`);
    
    // Check if socket.io requests were made
    console.log(`Socket.IO network requests: ${networkRequests.length}`);
    networkRequests.forEach((req, index) => {
      console.log(`  Request ${index + 1}: ${req.method} ${req.url}`);
      if (req.headers.authorization) {
        console.log(`    Has authorization header: ${req.headers.authorization.substring(0, 20)}...`);
      }
    });
    
    // Check console messages for socket-related logs
    const socketMessages = consoleMessages.filter(msg => 
      msg.includes('socket') || msg.includes('Socket') || 
      msg.includes('connect') || msg.includes('Connect') ||
      msg.includes('auth') || msg.includes('token')
    );
    
    console.log(`Socket-related console messages: ${socketMessages.length}`);
    socketMessages.forEach((msg, index) => {
      console.log(`  Message ${index + 1}: ${msg}`);
    });
    
    // Try to manually check socket connection in browser
    const socketConnectionInfo = await page.evaluate(() => {
      // Check if socket is available in window
      const socket = (window as any).socket;
      if (socket) {
        return {
          connected: socket.connected,
          id: socket.id,
          transport: socket.io.engine.transport.name
        };
      }
      return null;
    });
    
    if (socketConnectionInfo) {
      console.log('Socket connection info:', socketConnectionInfo);
    } else {
      console.log('No socket found in window object');
    }
    
    // Check if message input is enabled after waiting
    const messageInput = page.locator('[data-testid="message-input"]');
    const isEnabled = await messageInput.evaluate((el: HTMLTextAreaElement) => !el.disabled);
    console.log(`Message input enabled: ${isEnabled}`);
    
    if (!isEnabled) {
      const placeholder = await messageInput.getAttribute('placeholder');
      console.log(`Message input placeholder: ${placeholder}`);
    }
    
    console.log('Socket connection debugging completed');
  });
  
  test('should test socket connection with manual token', async ({ page }) => {
    console.log('Testing socket connection with manual token extraction');
    
    // Login first
    await loginUser(page);
    console.log('✅ Logged in successfully');
    
    // Extract token from localStorage or sessionStorage
    const authData = await page.evaluate(() => {
      const localStorage = window.localStorage;
      const sessionStorage = window.sessionStorage;
      
      return {
        localStorage: {
          token: localStorage.getItem('token'),
          authData: localStorage.getItem('authData'),
          user: localStorage.getItem('user')
        },
        sessionStorage: {
          token: sessionStorage.getItem('token'),
          authData: sessionStorage.getItem('authData'),
          user: sessionStorage.getItem('user')
        }
      };
    });
    
    console.log('Auth data from storage:', JSON.stringify(authData, null, 2));
    
    // Try to manually create socket connection
    const manualSocketTest = await page.evaluate((authData) => {
      return new Promise((resolve) => {
        try {
          // Try to find token
          let token = null;
          if (authData.localStorage.token) {
            token = authData.localStorage.token;
          } else if (authData.localStorage.authData) {
            const parsed = JSON.parse(authData.localStorage.authData);
            token = parsed.tokens?.access;
          }
          
          if (!token) {
            resolve({ error: 'No token found' });
            return;
          }
          
          // Try to connect manually
          const io = (window as any).io;
          if (!io) {
            resolve({ error: 'Socket.IO not available' });
            return;
          }
          
          const socket = io('http://localhost:7000', {
            auth: { token: token }
          });
          
          socket.on('connect', () => {
            resolve({ success: true, socketId: socket.id });
            socket.disconnect();
          });
          
          socket.on('connect_error', (error: any) => {
            resolve({ error: error.message });
            socket.disconnect();
          });
          
          // Timeout after 5 seconds
          setTimeout(() => {
            resolve({ error: 'Connection timeout' });
            socket.disconnect();
          }, 5000);
          
        } catch (error) {
          resolve({ error: (error as Error).message });
        }
      });
    }, authData);
    
    console.log('Manual socket test result:', manualSocketTest);
  });
});
