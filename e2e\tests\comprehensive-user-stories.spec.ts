// e2e/tests/comprehensive-user-stories.spec.ts
import { test, expect, Page, BrowserContext } from '@playwright/test';

// Test configuration
const FRONTEND_URL = 'http://localhost:5001';
const BACKEND_URL = 'http://localhost:6000';
const SOCKET_URL = 'http://localhost:7000';

// Test credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpass123'
};

const TARGET_USER = 'harry';

// Helper functions
async function loginUser(page: Page, email: string, password: string) {
  await page.goto(FRONTEND_URL);

  // Wait for login form to be visible (form element without specific test-id)
  await page.waitForSelector('form', { timeout: 10000 });

  // Fill login form
  await page.fill('[data-testid="email-input"]', email);
  await page.fill('[data-testid="password-input"]', password);

  // Submit login
  await page.click('[data-testid="login-button"]');

  // Wait for successful login (dashboard header should be visible)
  await page.waitForSelector('[data-testid="dashboard-header"]', { timeout: 15000 });
}

async function searchForUser(page: Page, username: string) {
  // Click new chat button to open user search modal
  await page.click('[data-testid="new-chat-button"]');

  // Wait for user search modal to appear
  await page.waitForSelector('[data-testid="user-search-modal"]', { timeout: 10000 });

  // Fill search input
  await page.fill('[data-testid="user-search-input"]', username);

  // Wait for search results
  await page.waitForSelector('[data-testid="user-search-results"]', { timeout: 5000 });
}

async function createOneToOneChat(page: Page, username: string) {
  await searchForUser(page, username);

  // Click on user action button to create chat
  await page.click('[data-testid="user-action-button"]');

  // Wait for chat area to be visible (conversation should be selected)
  await page.waitForSelector('[data-testid="chat-area"]', { timeout: 10000 });
}

async function sendMessage(page: Page, message: string) {
  // Wait for message input
  await page.waitForSelector('[data-testid="message-input"]', { timeout: 5000 });
  
  // Type message
  await page.fill('[data-testid="message-input"]', message);
  
  // Send message
  await page.click('[data-testid="send-button"]');
  
  // Wait for message to appear in chat
  await page.waitForSelector(`[data-testid="message"]:has-text("${message}")`, { timeout: 10000 });
}

async function uploadFile(page: Page, filePath: string) {
  // Wait for file upload button
  await page.waitForSelector('[data-testid="file-upload-button"]', { timeout: 5000 });
  
  // Upload file
  const fileInput = page.locator('input[type="file"]');
  await fileInput.setInputFiles(filePath);
  
  // Wait for file to be uploaded and appear in chat
  await page.waitForSelector('[data-testid="media-message"]', { timeout: 15000 });
}

async function createGroupChat(page: Page, groupName: string, description: string, members: string[]) {
  // Click create group chat button
  await page.click('[data-testid="create-group-button"]');
  
  // Fill group details
  await page.fill('[data-testid="group-name-input"]', groupName);
  await page.fill('[data-testid="group-description-input"]', description);
  
  // Add members
  for (const member of members) {
    await page.fill('[data-testid="member-search"]', member);
    await page.click(`[data-testid="add-member-${member}"]`);
  }
  
  // Create group
  await page.click('[data-testid="create-group-submit"]');
  
  // Wait for group chat to be created
  await page.waitForSelector('[data-testid="chat-room"]', { timeout: 10000 });
}

// Test Suite
test.describe('Comprehensive Chat Application E2E Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Set up network monitoring
    await page.route('**/*', (route) => {
      console.log(`Request: ${route.request().method()} ${route.request().url()}`);
      route.continue();
    });
    
    // Monitor console logs
    page.on('console', (msg) => {
      console.log(`Console ${msg.type()}: ${msg.text()}`);
    });
    
    // Monitor page errors
    page.on('pageerror', (error) => {
      console.error(`Page error: ${error.message}`);
    });
  });

  test('Test Case 1: One-to-One Chat Creation and Persistence', async ({ page }) => {
    console.log('Starting Test Case 1: One-to-One Chat Creation and Persistence');
    
    // Step 1: Login
    await loginUser(page, TEST_USER.email, TEST_USER.password);
    console.log('✅ User logged in successfully');
    
    // Step 2: Navigate to dashboard (already there after login)
    await expect(page.locator('[data-testid="dashboard-header"]')).toBeVisible();
    console.log('✅ Dashboard is visible');
    
    // Step 3: Search for user "harry"
    await searchForUser(page, TARGET_USER);
    console.log('✅ User search completed');
    
    // Step 4: Create one-to-one chat
    await createOneToOneChat(page, TARGET_USER);
    console.log('✅ One-to-one chat created');
    
    // Step 5: Verify temporary chat appears in Redux state
    const reduxState = await page.evaluate(() => {
      return (window as any).store?.getState?.();
    });
    
    if (reduxState) {
      console.log('Redux state:', JSON.stringify(reduxState.conversations, null, 2));
      expect(Object.keys(reduxState.conversations.conversations || {})).toHaveLength.greaterThan(0);
      console.log('✅ Chat appears in Redux state');
    }
    
    // Step 6: Refresh the page and verify chat persists
    await page.reload();
    await page.waitForSelector('[data-testid="dashboard-header"]', { timeout: 15000 });

    // Check if chat still exists after refresh (look for conversation list items)
    const chatExists = await page.locator('[data-testid="conversation-list"] .hover\\:bg-gray-50').count();
    expect(chatExists).toBeGreaterThan(0);
    console.log('✅ Chat persists after page refresh');
    
    console.log('✅ Test Case 1 completed successfully');
  });

  test('Test Case 2: One-to-One Real-time Messaging', async ({ browser }) => {
    console.log('Starting Test Case 2: One-to-One Real-time Messaging');
    
    // Create two browser contexts for real-time testing
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    
    // Set up monitoring for both pages
    [page1, page2].forEach((page, index) => {
      page.on('console', (msg) => {
        console.log(`Page${index + 1} Console ${msg.type()}: ${msg.text()}`);
      });
    });
    
    try {
      // Step 1: Login with first user
      await loginUser(page1, TEST_USER.email, TEST_USER.password);
      console.log('✅ User 1 logged in');
      
      // Step 2: Create one-to-one chat
      await createOneToOneChat(page1, TARGET_USER);
      console.log('✅ One-to-one chat created');
      
      // Step 3: Send message "hii"
      await sendMessage(page1, 'hii');
      console.log('✅ Message sent from user 1');
      
      // Step 4: Verify new conversation is created
      const conversationExists = await page1.locator('[data-testid="conversation-list"] .hover\\:bg-gray-50').count();
      expect(conversationExists).toBeGreaterThan(0);
      console.log('✅ New conversation created');
      
      // Step 5: In second browser, login as target user and verify message appears
      await loginUser(page2, '<EMAIL>', 'testpass123'); // Assuming harry's credentials
      
      // Wait for real-time message to appear
      await page2.waitForSelector('[data-testid="message"]:has-text("hii")', { timeout: 15000 });
      console.log('✅ Real-time message received in second browser');
      
      console.log('✅ Test Case 2 completed successfully');
      
    } finally {
      await context1.close();
      await context2.close();
    }
  });

  test('Test Case 3: Group Chat Real-time Messaging', async ({ browser }) => {
    console.log('Starting Test Case 3: Group Chat Real-time Messaging');
    
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    
    try {
      // Step 1: Login with first user
      await loginUser(page1, TEST_USER.email, TEST_USER.password);
      console.log('✅ User 1 logged in');
      
      // Step 2: Create group chat
      await createGroupChat(page1, 'Test Group', 'Test group description', [TARGET_USER]);
      console.log('✅ Group chat created');
      
      // Step 3: Send message "hii"
      await sendMessage(page1, 'hii');
      console.log('✅ Message sent to group');
      
      // Step 4: Verify new conversation is created
      const conversationExists = await page1.locator('[data-testid="conversation-list"] [data-testid*="conversation"]').count();
      expect(conversationExists).toBeGreaterThan(0);
      console.log('✅ New group conversation created');
      
      // Step 5: In second browser, verify message appears
      await loginUser(page2, '<EMAIL>', 'testpass123');
      
      // Wait for real-time message to appear
      await page2.waitForSelector('[data-testid="message"]:has-text("hii")', { timeout: 15000 });
      console.log('✅ Real-time group message received');
      
      console.log('✅ Test Case 3 completed successfully');
      
    } finally {
      await context1.close();
      await context2.close();
    }
  });

  test('Test Case 4: One-to-One File Sharing', async ({ browser }) => {
    console.log('Starting Test Case 4: One-to-One File Sharing');

    const context1 = await browser.newContext();
    const context2 = await browser.newContext();

    const page1 = await context1.newPage();
    const page2 = await context2.newPage();

    try {
      // Step 1: Login with first user
      await loginUser(page1, TEST_USER.email, TEST_USER.password);
      console.log('✅ User 1 logged in');

      // Step 2: Create one-to-one chat
      await createOneToOneChat(page1, TARGET_USER);
      console.log('✅ One-to-one chat created');

      // Step 3: Create a test file to upload
      const testFilePath = './test-file.txt';

      // Step 4: Send a file
      await uploadFile(page1, testFilePath);
      console.log('✅ File uploaded');

      // Step 5: Verify new conversation is created
      const conversationExists = await page1.locator('[data-testid="conversation-list"] [data-testid*="conversation"]').count();
      expect(conversationExists).toBeGreaterThan(0);
      console.log('✅ New conversation with file created');

      // Step 6: In second browser, verify file appears
      await loginUser(page2, '<EMAIL>', 'testpass123');

      // Wait for file to appear
      await page2.waitForSelector('[data-testid="media-message"]', { timeout: 15000 });
      console.log('✅ Real-time file sharing verified');

      console.log('✅ Test Case 4 completed successfully');

    } finally {
      await context1.close();
      await context2.close();
    }
  });

  test('Test Case 5: Group Chat File Sharing', async ({ browser }) => {
    console.log('Starting Test Case 5: Group Chat File Sharing');

    const context1 = await browser.newContext();
    const context2 = await browser.newContext();

    const page1 = await context1.newPage();
    const page2 = await context2.newPage();

    try {
      // Step 1: Login with first user
      await loginUser(page1, TEST_USER.email, TEST_USER.password);
      console.log('✅ User 1 logged in');

      // Step 2: Create group chat
      await createGroupChat(page1, 'File Share Group', 'Group for file sharing', [TARGET_USER]);
      console.log('✅ Group chat created');

      // Step 3: Create a test file to upload
      const testFilePath = './test-file.txt';

      // Step 4: Send a file
      await uploadFile(page1, testFilePath);
      console.log('✅ File uploaded to group');

      // Step 5: Verify new conversation is created
      const conversationExists = await page1.locator('[data-testid="conversation-list"] [data-testid*="conversation"]').count();
      expect(conversationExists).toBeGreaterThan(0);
      console.log('✅ New group conversation with file created');

      // Step 6: In second browser, verify file appears
      await loginUser(page2, '<EMAIL>', 'testpass123');

      // Wait for file to appear
      await page2.waitForSelector('[data-testid="media-message"]', { timeout: 15000 });
      console.log('✅ Real-time group file sharing verified');

      console.log('✅ Test Case 5 completed successfully');

    } finally {
      await context1.close();
      await context2.close();
    }
  });

  test('Security Testing: Network Traffic Analysis', async ({ page }) => {
    console.log('Starting Security Testing: Network Traffic Analysis');

    const networkRequests: any[] = [];
    const responses: any[] = [];

    // Monitor all network requests
    page.on('request', (request) => {
      networkRequests.push({
        url: request.url(),
        method: request.method(),
        headers: request.headers(),
        postData: request.postData()
      });
    });

    page.on('response', (response) => {
      responses.push({
        url: response.url(),
        status: response.status(),
        headers: response.headers()
      });
    });

    // Step 1: Login and perform actions
    await loginUser(page, TEST_USER.email, TEST_USER.password);
    await createOneToOneChat(page, TARGET_USER);
    await sendMessage(page, 'Security test message');

    // Step 2: Analyze network traffic
    console.log('Network Requests Analysis:');
    networkRequests.forEach((req, index) => {
      console.log(`Request ${index + 1}:`);
      console.log(`  URL: ${req.url}`);
      console.log(`  Method: ${req.method}`);

      // Check for sensitive data in plain text
      if (req.postData) {
        const postData = req.postData.toLowerCase();
        if (postData.includes('password') && !req.url.includes('https')) {
          console.error('❌ SECURITY ISSUE: Password sent over HTTP');
        }
        if (postData.includes('token') && !req.url.includes('https')) {
          console.error('❌ SECURITY ISSUE: Token sent over HTTP');
        }
      }

      // Check for proper authentication headers
      if (req.url.includes('/api/') && !req.headers.authorization) {
        console.warn('⚠️  API request without authorization header');
      }
    });

    console.log('Response Analysis:');
    responses.forEach((res, index) => {
      console.log(`Response ${index + 1}:`);
      console.log(`  URL: ${res.url}`);
      console.log(`  Status: ${res.status}`);

      // Check for security headers
      if (!res.headers['x-content-type-options']) {
        console.warn('⚠️  Missing X-Content-Type-Options header');
      }
      if (!res.headers['x-frame-options']) {
        console.warn('⚠️  Missing X-Frame-Options header');
      }
    });

    console.log('✅ Security analysis completed');
  });

  test('Console Logs and Error Analysis', async ({ page }) => {
    console.log('Starting Console Logs and Error Analysis');

    const consoleMessages: any[] = [];
    const pageErrors: any[] = [];

    // Monitor console messages
    page.on('console', (msg) => {
      consoleMessages.push({
        type: msg.type(),
        text: msg.text(),
        location: msg.location()
      });
    });

    // Monitor page errors
    page.on('pageerror', (error) => {
      pageErrors.push({
        message: error.message,
        stack: error.stack
      });
    });

    // Perform comprehensive user actions
    await loginUser(page, TEST_USER.email, TEST_USER.password);
    await createOneToOneChat(page, TARGET_USER);
    await sendMessage(page, 'Test message for error analysis');

    // Analyze console messages
    console.log('Console Messages Analysis:');
    const errorMessages = consoleMessages.filter(msg => msg.type === 'error');
    const warningMessages = consoleMessages.filter(msg => msg.type === 'warning');

    if (errorMessages.length > 0) {
      console.error('❌ Console Errors Found:');
      errorMessages.forEach((error, index) => {
        console.error(`  Error ${index + 1}: ${error.text}`);
        console.error(`    Location: ${JSON.stringify(error.location)}`);
      });
    } else {
      console.log('✅ No console errors found');
    }

    if (warningMessages.length > 0) {
      console.warn('⚠️  Console Warnings Found:');
      warningMessages.forEach((warning, index) => {
        console.warn(`  Warning ${index + 1}: ${warning.text}`);
      });
    } else {
      console.log('✅ No console warnings found');
    }

    // Analyze page errors
    if (pageErrors.length > 0) {
      console.error('❌ Page Errors Found:');
      pageErrors.forEach((error, index) => {
        console.error(`  Page Error ${index + 1}: ${error.message}`);
        console.error(`    Stack: ${error.stack}`);
      });
    } else {
      console.log('✅ No page errors found');
    }

    console.log('✅ Console and error analysis completed');
  });
});
