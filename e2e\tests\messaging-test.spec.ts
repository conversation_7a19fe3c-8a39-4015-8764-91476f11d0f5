// e2e/tests/messaging-test.spec.ts
import { test, expect, Page } from '@playwright/test';

const FRONTEND_URL = 'http://localhost:5001';

async function loginUser(page: Page) {
  await page.goto(FRONTEND_URL);
  await page.waitForSelector('form', { timeout: 10000 });
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'testpass123');
  await page.click('[data-testid="login-button"]');
  await page.waitForSelector('[data-testid="dashboard-header"]', { timeout: 15000 });
}

async function createChatWithHarry(page: Page) {
  await page.click('[data-testid="new-chat-button"]');
  await page.waitForSelector('[data-testid="user-search-modal"]', { timeout: 10000 });
  await page.fill('[data-testid="user-search-input"]', 'harry');
  await page.waitForTimeout(2000); // Wait for debounced search
  await page.click('[data-testid="user-action-button"]');
  await page.waitForSelector('[data-testid="chat-area"]', { timeout: 10000 });

  // Wait for socket connection to be established
  await page.waitForSelector('[data-testid="connection-status"]:has-text("Connected")', { timeout: 15000 });

  // Wait for message input to be enabled
  await page.waitForFunction(() => {
    const input = document.querySelector('[data-testid="message-input"]') as HTMLTextAreaElement;
    return input && !input.disabled;
  }, { timeout: 10000 });
}

test.describe('Messaging Test', () => {
  test('should send and receive messages', async ({ page }) => {
    console.log('Starting messaging test');
    
    // Login and create chat
    await loginUser(page);
    console.log('✅ Logged in successfully');
    
    await createChatWithHarry(page);
    console.log('✅ Chat created with Harry');
    
    // Send a message
    const testMessage = 'Hello Harry! This is a test message.';
    await page.fill('[data-testid="message-input"]', testMessage);
    await page.click('[data-testid="send-button"]');
    console.log('✅ Message sent');
    
    // Wait for message to appear in the chat
    await page.waitForSelector(`[data-testid="message"]:has-text("${testMessage}")`, { timeout: 10000 });
    console.log('✅ Message appears in chat');
    
    // Verify message content
    const messageElement = await page.locator(`[data-testid="message"]:has-text("${testMessage}")`).first();
    await expect(messageElement).toBeVisible();
    console.log('✅ Message is visible and correct');
    
    // Check if conversation appears in the sidebar
    const conversationList = await page.locator('[data-testid="conversation-list"]');
    await expect(conversationList).toBeVisible();
    console.log('✅ Conversation list is visible');
    
    // Send another message to test real-time functionality
    const secondMessage = 'This is a second test message.';
    await page.fill('[data-testid="message-input"]', secondMessage);
    await page.click('[data-testid="send-button"]');
    console.log('✅ Second message sent');
    
    // Wait for second message to appear
    await page.waitForSelector(`[data-testid="message"]:has-text("${secondMessage}")`, { timeout: 10000 });
    console.log('✅ Second message appears in chat');
    
    // Verify both messages are visible
    const allMessages = await page.locator('[data-testid="message"]').count();
    console.log(`Total messages visible: ${allMessages}`);
    expect(allMessages).toBeGreaterThanOrEqual(2);
    console.log('✅ Multiple messages are visible');
  });
});
