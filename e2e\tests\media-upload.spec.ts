// e2e/media-upload.spec.ts
import { test, expect, Page } from '@playwright/test';
import path from 'path';

// Test configuration
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpass123'
};

const BASE_URL = 'http://localhost:5002';
const API_BASE_URL = 'http://127.0.0.1:6000';

// Helper function to login
async function loginUser(page: Page) {
  console.log('🔐 Logging in user...');
  
  await page.goto(`${BASE_URL}/login`);
  await page.waitForLoadState('networkidle');
  
  // Fill login form
  await page.fill('input[type="email"]', TEST_USER.email);
  await page.fill('input[type="password"]', TEST_USER.password);
  
  // Click login button
  await page.click('button[type="submit"]');
  
  // Wait for redirect to dashboard
  await page.waitForURL('**/dashboard', { timeout: 10000 });
  console.log('✅ User logged in successfully');
}

// Helper function to navigate to a chat conversation
async function navigateToChat(page: Page) {
  console.log('💬 Navigating to chat conversation...');
  
  // Wait for conversations to load
  await page.waitForSelector('[data-testid="conversation-list"]', { timeout: 10000 });
  
  // Click on the first conversation or create one if none exists
  const conversations = await page.locator('[data-testid="conversation-item"]');
  const conversationCount = await conversations.count();
  
  if (conversationCount > 0) {
    await conversations.first().click();
    console.log('✅ Clicked on existing conversation');
  } else {
    // Create a new conversation if none exists
    await page.click('[data-testid="new-conversation-button"]');
    await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
    await page.waitForSelector('[data-testid="user-search-result"]');
    await page.click('[data-testid="user-search-result"]');
    await page.click('[data-testid="start-conversation-button"]');
    console.log('✅ Created new conversation');
  }
  
  // Wait for chat interface to load
  await page.waitForSelector('[data-testid="message-input"]', { timeout: 10000 });
  console.log('✅ Chat interface loaded');
}

// Helper function to create test files
function createTestFiles() {
  const testDir = path.join(__dirname, 'test-files');
  
  return {
    image: path.join(testDir, 'test-image.jpg'),
    document: path.join(testDir, 'test-document.pdf'),
    video: path.join(testDir, 'test-video.mp4'),
  };
}

test.describe('Media Upload Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test environment
    await page.setViewportSize({ width: 1280, height: 720 });
    
    // Enable console logging for debugging
    page.on('console', msg => {
      if (msg.type() === 'log' && msg.text().includes('🔍')) {
        console.log('Frontend Log:', msg.text());
      }
    });
    
    // Monitor network requests
    page.on('request', request => {
      if (request.url().includes('/api/media/')) {
        console.log('📡 Media API Request:', request.method(), request.url());
      }
    });
    
    page.on('response', response => {
      if (response.url().includes('/api/media/')) {
        console.log('📡 Media API Response:', response.status(), response.url());
      }
    });
  });

  test('should display media upload button', async ({ page }) => {
    await loginUser(page);
    await navigateToChat(page);
    
    // Check if media upload button is visible
    const mediaUploadButton = page.locator('[data-testid="media-upload-button"]');
    await expect(mediaUploadButton).toBeVisible();
    
    // Check if button has correct icon (Plus icon)
    const plusIcon = mediaUploadButton.locator('svg');
    await expect(plusIcon).toBeVisible();
    
    console.log('✅ Media upload button is visible');
  });

  test('should open upload dialog when plus button is clicked', async ({ page }) => {
    await loginUser(page);
    await navigateToChat(page);
    
    // Click the media upload button
    await page.click('[data-testid="media-upload-button"]');
    
    // Wait for dialog to appear
    await page.waitForSelector('text=Share Media', { timeout: 5000 });
    
    // Check if all upload options are present
    await expect(page.locator('text=Upload file from device')).toBeVisible();
    await expect(page.locator('text=Upload image from device')).toBeVisible();
    
    // Check if dialog can be closed
    await page.click('button:has-text("×")');
    await expect(page.locator('text=Share Media')).not.toBeVisible();
    
    console.log('✅ Upload dialog opens and closes correctly');
  });

  test('should handle image file selection and show preview', async ({ page }) => {
    await loginUser(page);
    await navigateToChat(page);
    
    // Create a test image file
    const testImageContent = Buffer.from('fake-image-data');
    
    // Click media upload button
    await page.click('[data-testid="media-upload-button"]');
    await page.waitForSelector('text=Share Media');
    
    // Set up file chooser handler
    const fileChooserPromise = page.waitForEvent('filechooser');
    
    // Click on image upload option
    await page.click('text=Upload image from device');
    
    const fileChooser = await fileChooserPromise;
    
    // Create a temporary test file
    const fs = require('fs');
    const os = require('os');
    const testImagePath = path.join(os.tmpdir(), 'test-image.jpg');
    fs.writeFileSync(testImagePath, testImageContent);
    
    // Select the test file
    await fileChooser.setFiles([testImagePath]);
    
    // Wait for preview modal to appear
    await page.waitForSelector('text=Preview Media', { timeout: 10000 });
    
    // Check if preview shows the selected file
    await expect(page.locator('text=test-image.jpg')).toBeVisible();
    
    // Check if Send button is present
    await expect(page.locator('button:has-text("Send")')).toBeVisible();
    
    // Clean up
    fs.unlinkSync(testImagePath);
    
    console.log('✅ Image file selection and preview works correctly');
  });

  test('should handle document file selection', async ({ page }) => {
    await loginUser(page);
    await navigateToChat(page);
    
    // Create a test document file
    const testDocContent = Buffer.from('fake-pdf-content');
    
    // Click media upload button
    await page.click('[data-testid="media-upload-button"]');
    await page.waitForSelector('text=Share Media');
    
    // Set up file chooser handler
    const fileChooserPromise = page.waitForEvent('filechooser');
    
    // Click on file upload option
    await page.click('text=Upload file from device');
    
    const fileChooser = await fileChooserPromise;
    
    // Create a temporary test file
    const fs = require('fs');
    const os = require('os');
    const testDocPath = path.join(os.tmpdir(), 'test-document.pdf');
    fs.writeFileSync(testDocPath, testDocContent);
    
    // Select the test file
    await fileChooser.setFiles([testDocPath]);
    
    // Wait for preview modal to appear
    await page.waitForSelector('text=Preview Media', { timeout: 10000 });
    
    // Check if preview shows the selected file
    await expect(page.locator('text=test-document.pdf')).toBeVisible();
    
    // Clean up
    fs.unlinkSync(testDocPath);
    
    console.log('✅ Document file selection works correctly');
  });

  test('should handle multiple file selection', async ({ page }) => {
    await loginUser(page);
    await navigateToChat(page);
    
    // Create test files
    const testFiles = [];
    const fs = require('fs');
    const os = require('os');
    
    for (let i = 1; i <= 3; i++) {
      const filePath = path.join(os.tmpdir(), `test-file-${i}.jpg`);
      fs.writeFileSync(filePath, Buffer.from(`fake-image-data-${i}`));
      testFiles.push(filePath);
    }
    
    // Click media upload button
    await page.click('[data-testid="media-upload-button"]');
    await page.waitForSelector('text=Share Media');
    
    // Set up file chooser handler
    const fileChooserPromise = page.waitForEvent('filechooser');
    
    // Click on image upload option
    await page.click('text=Upload image from device');
    
    const fileChooser = await fileChooserPromise;
    
    // Select multiple test files
    await fileChooser.setFiles(testFiles);
    
    // Wait for preview modal to appear
    await page.waitForSelector('text=Preview Media', { timeout: 10000 });
    
    // Check if preview shows all selected files
    await expect(page.locator('text=Preview Media (3 files)')).toBeVisible();
    
    // Clean up
    testFiles.forEach(filePath => fs.unlinkSync(filePath));
    
    console.log('✅ Multiple file selection works correctly');
  });

  test('should handle file removal from preview', async ({ page }) => {
    await loginUser(page);
    await navigateToChat(page);
    
    // Create a test file
    const fs = require('fs');
    const os = require('os');
    const testFilePath = path.join(os.tmpdir(), 'test-remove.jpg');
    fs.writeFileSync(testFilePath, Buffer.from('fake-image-data'));
    
    // Select file and open preview
    await page.click('[data-testid="media-upload-button"]');
    await page.waitForSelector('text=Share Media');
    
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('text=Upload image from device');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles([testFilePath]);
    
    await page.waitForSelector('text=Preview Media', { timeout: 10000 });
    
    // Remove the file
    await page.click('button:has-text("×")'); // Remove file button
    
    // Check if preview modal closes (no files left)
    await expect(page.locator('text=Preview Media')).not.toBeVisible();
    
    // Clean up
    fs.unlinkSync(testFilePath);
    
    console.log('✅ File removal from preview works correctly');
  });

  test('should simulate successful file upload', async ({ page }) => {
    await loginUser(page);
    await navigateToChat(page);
    
    // Create a test file
    const fs = require('fs');
    const os = require('os');
    const testFilePath = path.join(os.tmpdir(), 'test-upload.jpg');
    fs.writeFileSync(testFilePath, Buffer.from('fake-image-data'));
    
    // Select file and open preview
    await page.click('[data-testid="media-upload-button"]');
    await page.waitForSelector('text=Share Media');
    
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('text=Upload image from device');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles([testFilePath]);
    
    await page.waitForSelector('text=Preview Media', { timeout: 10000 });
    
    // Click Send button
    await page.click('button:has-text("Send")');
    
    // Wait for upload progress
    await page.waitForSelector('text=Uploading files...', { timeout: 5000 });
    
    // Wait for upload completion (simulated)
    await page.waitForSelector('text=100%', { timeout: 10000 });
    
    // Check if preview modal closes after successful upload
    await page.waitForSelector('text=Preview Media', { state: 'hidden', timeout: 5000 });
    
    // Clean up
    fs.unlinkSync(testFilePath);
    
    console.log('✅ File upload simulation works correctly');
  });

  test('should handle upload cancellation', async ({ page }) => {
    await loginUser(page);
    await navigateToChat(page);
    
    // Create a test file
    const fs = require('fs');
    const os = require('os');
    const testFilePath = path.join(os.tmpdir(), 'test-cancel.jpg');
    fs.writeFileSync(testFilePath, Buffer.from('fake-image-data'));
    
    // Select file and open preview
    await page.click('[data-testid="media-upload-button"]');
    await page.waitForSelector('text=Share Media');
    
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('text=Upload image from device');
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles([testFilePath]);
    
    await page.waitForSelector('text=Preview Media', { timeout: 10000 });
    
    // Click Cancel button
    await page.click('button:has-text("Cancel")');
    
    // Check if preview modal closes
    await expect(page.locator('text=Preview Media')).not.toBeVisible();
    
    // Clean up
    fs.unlinkSync(testFilePath);
    
    console.log('✅ Upload cancellation works correctly');
  });

  test('should be disabled when user is disconnected', async ({ page }) => {
    await loginUser(page);
    await navigateToChat(page);
    
    // Simulate network disconnection by blocking Socket.IO
    await page.route('**/socket.io/**', route => route.abort());
    
    // Wait a moment for disconnection to be detected
    await page.waitForTimeout(2000);
    
    // Check if media upload button is disabled
    const mediaUploadButton = page.locator('[data-testid="media-upload-button"]');
    await expect(mediaUploadButton).toBeDisabled();
    
    console.log('✅ Media upload button is disabled when disconnected');
  });
});

test.describe('Media Upload Error Scenarios', () => {
  test('should handle file size validation', async ({ page }) => {
    await loginUser(page);
    await navigateToChat(page);
    
    // This test would require creating a large file or mocking the validation
    // For now, we'll test the UI behavior
    console.log('✅ File size validation test placeholder');
  });

  test('should handle unsupported file types', async ({ page }) => {
    await loginUser(page);
    await navigateToChat(page);
    
    // This test would require creating files with unsupported extensions
    // For now, we'll test the UI behavior
    console.log('✅ Unsupported file type test placeholder');
  });
});
