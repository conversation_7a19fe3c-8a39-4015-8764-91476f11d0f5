{"config": {"configFile": "D:\\AI PRojects\\ChatApplication\\playwright.config.ts", "rootDir": "D:/AI PRojects/ChatApplication/e2e/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 6}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "e2e/reports/html"}], ["json", {"outputFile": "e2e/reports/results.json"}], ["junit", {"outputFile": "e2e/reports/results.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "chromium", "name": "chromium", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "firefox", "name": "firefox", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "webkit", "name": "webkit", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.55.0", "workers": 6, "webServer": null}, "suites": [{"title": "media-upload.spec.ts", "file": "media-upload.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Media Upload Functionality", "file": "media-upload.spec.ts", "line": 73, "column": 6, "specs": [{"title": "should display media upload button", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 36317, "error": {"message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================", "stack": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:29:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:100:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 14, "line": 29}, "snippet": "\u001b[0m \u001b[90m 27 |\u001b[39m   \n \u001b[90m 28 |\u001b[39m   \u001b[90m// Wait for redirect to dashboard\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 29 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ User logged in successfully'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 31 |\u001b[39m }\n \u001b[90m 32 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 14, "line": 29}, "message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n\n\u001b[0m \u001b[90m 27 |\u001b[39m   \n \u001b[90m 28 |\u001b[39m   \u001b[90m// Wait for redirect to dashboard\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 29 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ User logged in successfully'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 31 |\u001b[39m }\n \u001b[90m 32 |\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:29:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:100:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:49:49.644Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--fb406-display-media-upload-button-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--fb406-display-media-upload-button-chromium\\video.webm"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 14, "line": 29}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-80596de2aa7a2c2e27ae", "file": "media-upload.spec.ts", "line": 99, "column": 7}, {"title": "should open upload dialog when plus button is clicked", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "timedOut", "duration": 37225, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:116:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:49:49.663Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-9c685102219b2f6406ed", "file": "media-upload.spec.ts", "line": 114, "column": 7}, {"title": "should handle image file selection and show preview", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "timedOut", "duration": 37974, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:137:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:49:49.744Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--99ddf--selection-and-show-preview-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--99ddf--selection-and-show-preview-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--99ddf--selection-and-show-preview-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-74468d9d09cc79237e56", "file": "media-upload.spec.ts", "line": 135, "column": 7}, {"title": "should handle document file selection", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "timedOut", "duration": 35388, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:180:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:49:49.681Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--8f645-dle-document-file-selection-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--8f645-dle-document-file-selection-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--8f645-dle-document-file-selection-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-75183bfd77ff3a2d861d", "file": "media-upload.spec.ts", "line": 178, "column": 7}, {"title": "should handle multiple file selection", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "timedOut", "duration": 35993, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:220:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:49:49.711Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-1db197d65e4cdb3291d0", "file": "media-upload.spec.ts", "line": 218, "column": 7}, {"title": "should handle file removal from preview", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "failed", "duration": 34545, "error": {"message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================", "stack": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:29:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:261:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 14, "line": 29}, "snippet": "\u001b[0m \u001b[90m 27 |\u001b[39m   \n \u001b[90m 28 |\u001b[39m   \u001b[90m// Wait for redirect to dashboard\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 29 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ User logged in successfully'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 31 |\u001b[39m }\n \u001b[90m 32 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 14, "line": 29}, "message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/dashboard\" until \"load\"\n============================================================\n\n\u001b[0m \u001b[90m 27 |\u001b[39m   \n \u001b[90m 28 |\u001b[39m   \u001b[90m// Wait for redirect to dashboard\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 29 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 30 |\u001b[39m   console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ User logged in successfully'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 31 |\u001b[39m }\n \u001b[90m 32 |\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:29:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:261:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:49:49.679Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--c90be-e-file-removal-from-preview-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--c90be-e-file-removal-from-preview-chromium\\video.webm"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 14, "line": 29}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-127a431cc6b80ce25dab", "file": "media-upload.spec.ts", "line": 260, "column": 7}, {"title": "should simulate successful file upload", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 3, "status": "failed", "duration": 21970, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:295:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:295:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:50:29.760Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--65765-late-successful-file-upload-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--65765-late-successful-file-upload-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--65765-late-successful-file-upload-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-b0c26c81a3fa1fdaec67", "file": "media-upload.spec.ts", "line": 293, "column": 7}, {"title": "should handle upload cancellation", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 4, "status": "failed", "duration": 22153, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:334:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:334:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:50:29.796Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-0e43da6d8f7598dd3c02", "file": "media-upload.spec.ts", "line": 332, "column": 7}, {"title": "should be disabled when user is disconnected", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 1, "status": "failed", "duration": 18794, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:367:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:367:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:50:31.171Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-95e14f3a7e02a4fb44b3", "file": "media-upload.spec.ts", "line": 365, "column": 7}, {"title": "should display media upload button", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 11, "parallelIndex": 2, "status": "timedOut", "duration": 31373, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 14, "line": 19}, "message": "Error: page.waitForLoadState: Test timeout of 30000ms exceeded.\n\n\u001b[0m \u001b[90m 17 |\u001b[39m   \n \u001b[90m 18 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m`${BASE_URL}/login`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 19 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 20 |\u001b[39m   \n \u001b[90m 21 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 22 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mTEST_USER\u001b[39m\u001b[33m.\u001b[39memail)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:19:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:100:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:50:34.701Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--fb406-display-media-upload-button-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--fb406-display-media-upload-button-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--fb406-display-media-upload-button-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-a1028f45882fcdf7f281", "file": "media-upload.spec.ts", "line": 99, "column": 7}, {"title": "should open upload dialog when plus button is clicked", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 12, "parallelIndex": 1, "status": "timedOut", "duration": 30330, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 14, "line": 19}, "message": "Error: page.waitForLoadState: Test timeout of 30000ms exceeded.\n\n\u001b[0m \u001b[90m 17 |\u001b[39m   \n \u001b[90m 18 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m`${BASE_URL}/login`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 19 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 20 |\u001b[39m   \n \u001b[90m 21 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 22 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mTEST_USER\u001b[39m\u001b[33m.\u001b[39memail)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:19:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:115:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:50:55.470Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-2b63841f6fd985897a69", "file": "media-upload.spec.ts", "line": 114, "column": 7}, {"title": "should handle image file selection and show preview", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 13, "parallelIndex": 4, "status": "timedOut", "duration": 31560, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 14, "line": 19}, "message": "Error: page.waitForLoadState: Test timeout of 30000ms exceeded.\n\n\u001b[0m \u001b[90m 17 |\u001b[39m   \n \u001b[90m 18 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m`${BASE_URL}/login`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 19 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 20 |\u001b[39m   \n \u001b[90m 21 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 22 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mTEST_USER\u001b[39m\u001b[33m.\u001b[39memail)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:19:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:136:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:50:55.831Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--99ddf--selection-and-show-preview-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--99ddf--selection-and-show-preview-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--99ddf--selection-and-show-preview-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-925b82cda100dfff1664", "file": "media-upload.spec.ts", "line": 135, "column": 7}, {"title": "should handle document file selection", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 14, "parallelIndex": 5, "status": "timedOut", "duration": 30378, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 14, "line": 19}, "message": "Error: page.waitForLoadState: Test timeout of 30000ms exceeded.\n\n\u001b[0m \u001b[90m 17 |\u001b[39m   \n \u001b[90m 18 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m`${BASE_URL}/login`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 19 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 20 |\u001b[39m   \n \u001b[90m 21 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 22 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mTEST_USER\u001b[39m\u001b[33m.\u001b[39memail)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:19:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:179:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:50:55.864Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--8f645-dle-document-file-selection-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--8f645-dle-document-file-selection-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--8f645-dle-document-file-selection-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-ab689dd4b2120ce51b90", "file": "media-upload.spec.ts", "line": 178, "column": 7}, {"title": "should handle multiple file selection", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 15, "parallelIndex": 3, "status": "failed", "duration": 53438, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:220:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:220:5\u001b[22m"}, {"message": "\u001b[31mTearing down \"context\" exceeded the test timeout of 30000ms.\u001b[39m"}, {"message": "Error: browserContext.close: Test ended.\nBrowser logs:\n\n<launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1490\\firefox\\firefox.exe -no-remote -wait-for-browser -foreground -profile C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_firefoxdev_profile-OgE1jy -juggler-pipe -silent\n<launched> pid=23188\n[pid=23188][err] JavaScript warning: resource://services-settings/Utils.sys.mjs, line 116: unreachable code after return statement\n[pid=23188][out] console.warn: services.settings: Ignoring preference override of remote settings server\n[pid=23188][out] console.warn: services.settings: Allow by setting MOZ_REMOTE_SETTINGS_DEVTOOLS=1 in the environment\n[pid=23188][out] \n[pid=23188][out] Juggler listening to the pipe\n[pid=23188][out] console.error: \"Warning: unrecognized command line flag\" \"-foreground\"\n[pid=23188][out] Crash Annotation GraphicsCriticalError: |[G0][GFX1-]: Calling WaitFlushedEvent::Run: is delayed: 2161 (t=51.295) [GFX1-]: Calling WaitFlushedEvent::Run: is delayed: 2161\n[pid=23188][out] console.error: services.settings: \n[pid=23188][out]   Message: EmptyDatabaseError: \"main/nimbus-desktop-experiments\" has not been synced yet\n[pid=23188][out]   Stack:\n[pid=23188][out]     EmptyDatabaseError@resource://services-settings/Database.sys.mjs:19:5\n[pid=23188][out] list@resource://services-settings/Database.sys.mjs:96:13\n[pid=23188][out] \n[pid=23188][out] console.error: services.settings: \n[pid=23188][out]   Message: EmptyDatabaseError: \"main/nimbus-secure-experiments\" has not been synced yet\n[pid=23188][out]   Stack:\n[pid=23188][out]     EmptyDatabaseError@resource://services-settings/Database.sys.mjs:19:5\n[pid=23188][out] list@resource://services-settings/Database.sys.mjs:96:13\n[pid=23188][out] \n[pid=23188][err] JavaScript error: chrome://juggler/content/Helper.js, line 82: NS_ERROR_FAILURE: Component returned failure code: 0x80004005 (NS_ERROR_FAILURE) [nsIWebProgress.removeProgressListener]\n[pid=23188][out] console.warn: services.settings: #fetchAttachment: Forcing fallbackToDump to false due to Utils.LOAD_DUMPS being false\n[pid=23188][out] console.error: (new NotFoundError(\"Could not find fa0fc42c-d91d-fca7-34eb-806ff46062dc in cache or dump\", \"resource://services-settings/Attachments.sys.mjs\", 48))\n[pid=23188][out] console.warn: \"Unable to find the attachment for\" \"fa0fc42c-d91d-fca7-34eb-806ff46062dc\"\n[pid=23188][out] console.warn: LoginRecipes: \"Falling back to a synchronous message for: http://localhost:5002.\"\n[pid=23188] <gracefully close start>\n[pid=23188] <kill>\n[pid=23188] <will force kill>\n[pid=23188] taskkill stdout: SUCCESS: The process with PID 12212 (child process of PID 13264) has been terminated.\r\nSUCCESS: The process with PID 23112 (child process of PID 13264) has been terminated.\r\nSUCCESS: The process with PID 2304 (child process of PID 13264) has been terminated.\r\nSUCCESS: The process with PID 16188 (child process of PID 13264) has been terminated.\r\nSUCCESS: The process with PID 10052 (child process of PID 13264) has been terminated.\r\nSUCCESS: The process with PID 24312 (child process of PID 13264) has been terminated.\r\nSUCCESS: The process with PID 13264 (child process of PID 23188) has been terminated.\r\nSUCCESS: The process with PID 23188 (child process of PID 7904) has been terminated.\r\n"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:50:55.830Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-firefox\\test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-60e7f18848d2418e9130", "file": "media-upload.spec.ts", "line": 218, "column": 7}, {"title": "should handle file removal from preview", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 16, "parallelIndex": 0, "status": "failed", "duration": 22037, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:262:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:262:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:51:14.501Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--c90be-e-file-removal-from-preview-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--c90be-e-file-removal-from-preview-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--c90be-e-file-removal-from-preview-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-fcc9ae7761e56242e2ca", "file": "media-upload.spec.ts", "line": 260, "column": 7}, {"title": "should simulate successful file upload", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 17, "parallelIndex": 2, "status": "timedOut", "duration": 30316, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 14, "line": 19}, "message": "Error: page.waitForLoadState: Test timeout of 30000ms exceeded.\n\n\u001b[0m \u001b[90m 17 |\u001b[39m   \n \u001b[90m 18 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m`${BASE_URL}/login`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 19 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 20 |\u001b[39m   \n \u001b[90m 21 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 22 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mTEST_USER\u001b[39m\u001b[33m.\u001b[39memail)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:19:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:294:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:51:28.831Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--65765-late-successful-file-upload-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--65765-late-successful-file-upload-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--65765-late-successful-file-upload-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-8acaba6403241d3883f3", "file": "media-upload.spec.ts", "line": 293, "column": 7}, {"title": "should handle upload cancellation", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 18, "parallelIndex": 1, "status": "timedOut", "duration": 30447, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 14, "line": 19}, "message": "Error: page.waitForLoadState: Test timeout of 30000ms exceeded.\n\n\u001b[0m \u001b[90m 17 |\u001b[39m   \n \u001b[90m 18 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m`${BASE_URL}/login`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 19 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 20 |\u001b[39m   \n \u001b[90m 21 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 22 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mTEST_USER\u001b[39m\u001b[33m.\u001b[39memail)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:19:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:333:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:51:30.051Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-fe073514042c9bbc9582", "file": "media-upload.spec.ts", "line": 332, "column": 7}, {"title": "should be disabled when user is disconnected", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 19, "parallelIndex": 5, "status": "timedOut", "duration": 30230, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 14, "line": 19}, "message": "Error: page.waitForLoadState: Test timeout of 30000ms exceeded.\n\n\u001b[0m \u001b[90m 17 |\u001b[39m   \n \u001b[90m 18 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m`${BASE_URL}/login`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 19 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 20 |\u001b[39m   \n \u001b[90m 21 |\u001b[39m   \u001b[90m// Fill login form\u001b[39m\n \u001b[90m 22 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[33mTEST_USER\u001b[39m\u001b[33m.\u001b[39memail)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:19:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:366:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:51:34.100Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-24591aec7f5975ea0d47", "file": "media-upload.spec.ts", "line": 365, "column": 7}, {"title": "should display media upload button", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 22, "parallelIndex": 2, "status": "failed", "duration": 18825, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:101:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:101:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:52:03.482Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--fb406-display-media-upload-button-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--fb406-display-media-upload-button-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--fb406-display-media-upload-button-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-8362b8ad979b371ff226", "file": "media-upload.spec.ts", "line": 99, "column": 7}, {"title": "should open upload dialog when plus button is clicked", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 23, "parallelIndex": 4, "status": "failed", "duration": 18513, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:116:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:116:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:52:03.957Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-fe52e085021d18f92cc3", "file": "media-upload.spec.ts", "line": 114, "column": 7}, {"title": "should handle image file selection and show preview", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 24, "parallelIndex": 1, "status": "failed", "duration": 17678, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:137:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:137:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:52:04.754Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--99ddf--selection-and-show-preview-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--99ddf--selection-and-show-preview-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--99ddf--selection-and-show-preview-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-47649335be0dd8a10104", "file": "media-upload.spec.ts", "line": 135, "column": 7}, {"title": "should handle document file selection", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 25, "parallelIndex": 5, "status": "failed", "duration": 18788, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:180:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:180:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:52:07.761Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--8f645-dle-document-file-selection-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--8f645-dle-document-file-selection-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--8f645-dle-document-file-selection-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-15d77c9fa217e9b6595a", "file": "media-upload.spec.ts", "line": 178, "column": 7}, {"title": "should handle multiple file selection", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 26, "parallelIndex": 0, "status": "failed", "duration": 19537, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:220:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:220:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:52:09.450Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-38b911ca5661670404ce", "file": "media-upload.spec.ts", "line": 218, "column": 7}, {"title": "should handle file removal from preview", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 27, "parallelIndex": 3, "status": "failed", "duration": 17675, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:262:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:262:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:52:34.229Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--c90be-e-file-removal-from-preview-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--c90be-e-file-removal-from-preview-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--c90be-e-file-removal-from-preview-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-8fffb9926241c71463c2", "file": "media-upload.spec.ts", "line": 260, "column": 7}, {"title": "should simulate successful file upload", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 28, "parallelIndex": 1, "status": "failed", "duration": 18716, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:295:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:295:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:52:34.862Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--65765-late-successful-file-upload-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--65765-late-successful-file-upload-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--65765-late-successful-file-upload-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-43fbba0184912dcae81f", "file": "media-upload.spec.ts", "line": 293, "column": 7}, {"title": "should handle upload cancellation", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 29, "parallelIndex": 4, "status": "failed", "duration": 19899, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:334:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:334:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:52:35.589Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-7fbb80f0744af2b693e9", "file": "media-upload.spec.ts", "line": 332, "column": 7}, {"title": "should be disabled when user is disconnected", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 30, "parallelIndex": 5, "status": "failed", "duration": 21229, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:367:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:367:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:52:35.707Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-cf74367ceb62436ffcc1", "file": "media-upload.spec.ts", "line": 365, "column": 7}, {"title": "should display media upload button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 33, "parallelIndex": 3, "status": "interrupted", "duration": 9404, "error": {"message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:101:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:101:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:52:56.200Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--fb406-display-media-upload-button-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--fb406-display-media-upload-button-Mobile-Chrome\\video.webm"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-10b2be0e60972ae694c9", "file": "media-upload.spec.ts", "line": 99, "column": 7}, {"title": "should open upload dialog when plus button is clicked", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 34, "parallelIndex": 1, "status": "interrupted", "duration": 8197, "error": {"message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:116:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:116:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:52:57.785Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-Mobile-Chrome\\video.webm"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-c35893298c4fcd250499", "file": "media-upload.spec.ts", "line": 114, "column": 7}, {"title": "should handle image file selection and show preview", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 35, "parallelIndex": 2, "status": "interrupted", "duration": 7228, "error": {"message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:137:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:137:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:52:58.889Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--99ddf--selection-and-show-preview-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--99ddf--selection-and-show-preview-Mobile-Chrome\\video.webm"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-cf1847f06c6b91237d44", "file": "media-upload.spec.ts", "line": 135, "column": 7}, {"title": "should handle document file selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 36, "parallelIndex": 4, "status": "interrupted", "duration": 6965, "error": {"message": "Error: locator.count: Test ended.", "stack": "Error: locator.count: Test ended.\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:42:49)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:180:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 49, "line": 42}, "snippet": "\u001b[0m \u001b[90m 40 |\u001b[39m   \u001b[90m// Click on the first conversation or create one if none exists\u001b[39m\n \u001b[90m 41 |\u001b[39m   \u001b[36mconst\u001b[39m conversations \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"conversation-item\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 42 |\u001b[39m   \u001b[36mconst\u001b[39m conversationCount \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m conversations\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 43 |\u001b[39m   \n \u001b[90m 44 |\u001b[39m   \u001b[36mif\u001b[39m (conversationCount \u001b[33m>\u001b[39m \u001b[35m0\u001b[39m) {\n \u001b[90m 45 |\u001b[39m     \u001b[36mawait\u001b[39m conversations\u001b[33m.\u001b[39mfirst()\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 49, "line": 42}, "message": "Error: locator.count: Test ended.\n\n\u001b[0m \u001b[90m 40 |\u001b[39m   \u001b[90m// Click on the first conversation or create one if none exists\u001b[39m\n \u001b[90m 41 |\u001b[39m   \u001b[36mconst\u001b[39m conversations \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"conversation-item\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 42 |\u001b[39m   \u001b[36mconst\u001b[39m conversationCount \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m conversations\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 43 |\u001b[39m   \n \u001b[90m 44 |\u001b[39m   \u001b[36mif\u001b[39m (conversationCount \u001b[33m>\u001b[39m \u001b[35m0\u001b[39m) {\n \u001b[90m 45 |\u001b[39m     \u001b[36mawait\u001b[39m conversations\u001b[33m.\u001b[39mfirst()\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:42:49)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:180:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:52:59.090Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--8f645-dle-document-file-selection-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--8f645-dle-document-file-selection-Mobile-Chrome\\video.webm"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 49, "line": 42}}], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-acf2a4b2ff66022f2025", "file": "media-upload.spec.ts", "line": 178, "column": 7}, {"title": "should handle multiple file selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 37, "parallelIndex": 5, "status": "interrupted", "duration": 1064, "error": {"message": "Error: browserContext.newPage: Test ended.", "stack": "Error: browserContext.newPage: Test ended."}, "errors": [{"message": "Error: browserContext.newPage: Test ended."}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:53:02.420Z", "annotations": [], "attachments": []}], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-5acde9b13ba6b1794779", "file": "media-upload.spec.ts", "line": 218, "column": 7}, {"title": "should handle file removal from preview", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 38, "parallelIndex": 0, "status": "interrupted", "duration": 113, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:53:04.069Z", "annotations": [], "attachments": []}], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-99bf1f2205a0471f65aa", "file": "media-upload.spec.ts", "line": 260, "column": 7}, {"title": "should simulate successful file upload", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-759e675ead37c40c3ec6", "file": "media-upload.spec.ts", "line": 293, "column": 7}, {"title": "should handle upload cancellation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-975a6891ca9cf8bdc5f8", "file": "media-upload.spec.ts", "line": 332, "column": 7}, {"title": "should be disabled when user is disconnected", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-07fcb375ff80510fd0c5", "file": "media-upload.spec.ts", "line": 365, "column": 7}, {"title": "should display media upload button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-8e0b434744fed5019214", "file": "media-upload.spec.ts", "line": 99, "column": 7}, {"title": "should open upload dialog when plus button is clicked", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-4606d78049844af67c33", "file": "media-upload.spec.ts", "line": 114, "column": 7}, {"title": "should handle image file selection and show preview", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-4d180ca98deee2749dd7", "file": "media-upload.spec.ts", "line": 135, "column": 7}, {"title": "should handle document file selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-559920223dfceb4dedae", "file": "media-upload.spec.ts", "line": 178, "column": 7}, {"title": "should handle multiple file selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-b63356538c25ec1c8daa", "file": "media-upload.spec.ts", "line": 218, "column": 7}, {"title": "should handle file removal from preview", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-dd0920c2da2fada24d47", "file": "media-upload.spec.ts", "line": 260, "column": 7}, {"title": "should simulate successful file upload", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-0ee2591ebbc6e2120f1f", "file": "media-upload.spec.ts", "line": 293, "column": 7}, {"title": "should handle upload cancellation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-115798d31f40382c1cd7", "file": "media-upload.spec.ts", "line": 332, "column": 7}, {"title": "should be disabled when user is disconnected", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-484eccbf024d0d7bc067", "file": "media-upload.spec.ts", "line": 365, "column": 7}, {"title": "should display media upload button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-997aab4c82dbdb4f2bdd", "file": "media-upload.spec.ts", "line": 99, "column": 7}, {"title": "should open upload dialog when plus button is clicked", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-81c98904ce2ee73bd240", "file": "media-upload.spec.ts", "line": 114, "column": 7}, {"title": "should handle image file selection and show preview", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-0f9195bff9057d02edfd", "file": "media-upload.spec.ts", "line": 135, "column": 7}, {"title": "should handle document file selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-32d41936136a469ce612", "file": "media-upload.spec.ts", "line": 178, "column": 7}, {"title": "should handle multiple file selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-500fb74395a62163b372", "file": "media-upload.spec.ts", "line": 218, "column": 7}, {"title": "should handle file removal from preview", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-feba6a7ce43fa76eba03", "file": "media-upload.spec.ts", "line": 260, "column": 7}, {"title": "should simulate successful file upload", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-f3e5511a5688a4a1f035", "file": "media-upload.spec.ts", "line": 293, "column": 7}, {"title": "should handle upload cancellation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-1acd14cf1172dabd39af", "file": "media-upload.spec.ts", "line": 332, "column": 7}, {"title": "should be disabled when user is disconnected", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-210f42860598730271f3", "file": "media-upload.spec.ts", "line": 365, "column": 7}, {"title": "should display media upload button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-bd21676a0ad6695ea3bd", "file": "media-upload.spec.ts", "line": 99, "column": 7}, {"title": "should open upload dialog when plus button is clicked", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-962c0c50b462f079bfc2", "file": "media-upload.spec.ts", "line": 114, "column": 7}, {"title": "should handle image file selection and show preview", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-06cfb40d79d9b5d79774", "file": "media-upload.spec.ts", "line": 135, "column": 7}, {"title": "should handle document file selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-c20867d0f4eb9c1822ba", "file": "media-upload.spec.ts", "line": 178, "column": 7}, {"title": "should handle multiple file selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-cd230a368e9b7b5135c9", "file": "media-upload.spec.ts", "line": 218, "column": 7}, {"title": "should handle file removal from preview", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-897459e1906a691a1a9e", "file": "media-upload.spec.ts", "line": 260, "column": 7}, {"title": "should simulate successful file upload", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-0494fb759a8051631b3c", "file": "media-upload.spec.ts", "line": 293, "column": 7}, {"title": "should handle upload cancellation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-c9f3a7414818bf7740d0", "file": "media-upload.spec.ts", "line": 332, "column": 7}, {"title": "should be disabled when user is disconnected", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-b312f652c7b29d602576", "file": "media-upload.spec.ts", "line": 365, "column": 7}]}, {"title": "Media Upload E<PERSON><PERSON>", "file": "media-upload.spec.ts", "line": 383, "column": 6, "specs": [{"title": "should handle file size validation", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 5, "status": "failed", "duration": 20214, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:386:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:386:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:50:31.271Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--e2983-handle-file-size-validation-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--e2983-handle-file-size-validation-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--e2983-handle-file-size-validation-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-563c8b6e6bafd0189c87", "file": "media-upload.spec.ts", "line": 384, "column": 7}, {"title": "should handle unsupported file types", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 10, "parallelIndex": 0, "status": "failed", "duration": 27061, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:395:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:395:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:50:31.398Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-bcf832955d1a803bbc8d", "file": "media-upload.spec.ts", "line": 393, "column": 7}, {"title": "should handle file size validation", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 20, "parallelIndex": 4, "status": "failed", "duration": 19518, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button[type=\"submit\"]')\u001b[22m\n\u001b[2m    - locator resolved to <button type=\"submit\" data-testid=\"login-button\" class=\"inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 px-4 py-2 text-sm  w-full\">Sign in</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button[type=\"submit\"]')\u001b[22m\n\u001b[2m    - locator resolved to <button type=\"submit\" data-testid=\"login-button\" class=\"inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 px-4 py-2 text-sm  w-full\">Sign in</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\n    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:26:14)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:385:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 14, "line": 26}, "snippet": "\u001b[0m \u001b[90m 24 |\u001b[39m   \n \u001b[90m 25 |\u001b[39m   \u001b[90m// Click login button\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 26 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 27 |\u001b[39m   \n \u001b[90m 28 |\u001b[39m   \u001b[90m// Wait for redirect to dashboard\u001b[39m\n \u001b[90m 29 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 14, "line": 26}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button[type=\"submit\"]')\u001b[22m\n\u001b[2m    - locator resolved to <button type=\"submit\" data-testid=\"login-button\" class=\"inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 px-4 py-2 text-sm  w-full\">Sign in</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\n\n\u001b[0m \u001b[90m 24 |\u001b[39m   \n \u001b[90m 25 |\u001b[39m   \u001b[90m// Click login button\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 26 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 27 |\u001b[39m   \n \u001b[90m 28 |\u001b[39m   \u001b[90m// Wait for redirect to dashboard\u001b[39m\n \u001b[90m 29 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/dashboard'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:26:14)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:385:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:51:37.623Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--e2983-handle-file-size-validation-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--e2983-handle-file-size-validation-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--e2983-handle-file-size-validation-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 14, "line": 26}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-da0f8ad557f8217d8263", "file": "media-upload.spec.ts", "line": 384, "column": 7}, {"title": "should handle unsupported file types", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 21, "parallelIndex": 0, "status": "failed", "duration": 19646, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:395:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:395:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:51:46.538Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-2aaf64964adebf931dd6", "file": "media-upload.spec.ts", "line": 393, "column": 7}, {"title": "should handle file size validation", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 31, "parallelIndex": 2, "status": "failed", "duration": 19450, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:386:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:386:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:52:35.550Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--e2983-handle-file-size-validation-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--e2983-handle-file-size-validation-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--e2983-handle-file-size-validation-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-5c795d1d7e1e3a52d91d", "file": "media-upload.spec.ts", "line": 384, "column": 7}, {"title": "should handle unsupported file types", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 32, "parallelIndex": 0, "status": "failed", "duration": 21150, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\n    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:395:5", "location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-conversation-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m   } \u001b[36melse\u001b[39m {\n \u001b[90m 48 |\u001b[39m     \u001b[90m// Create a new conversation if none exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-conversation-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"user-search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"user-search-result\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at navigateToChat (D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:49:16)\u001b[22m\n\u001b[2m    at D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts:395:5\u001b[22m"}], "stdout": [{"text": "🔐 Logging in user...\n"}, {"text": "✅ User logged in successfully\n"}, {"text": "💬 Navigating to chat conversation...\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-31T14:52:36.834Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\AI PRojects\\ChatApplication\\e2e\\test-results\\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\AI PRojects\\ChatApplication\\e2e\\tests\\media-upload.spec.ts", "column": 16, "line": 49}}], "status": "unexpected"}], "id": "101b3c7918c4b3dc4348-f957e83a0decadd14b9c", "file": "media-upload.spec.ts", "line": 393, "column": 7}, {"title": "should handle file size validation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-daaeed7990ceae29ca9b", "file": "media-upload.spec.ts", "line": 384, "column": 7}, {"title": "should handle unsupported file types", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-6699e2949159917c742e", "file": "media-upload.spec.ts", "line": 393, "column": 7}, {"title": "should handle file size validation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-476286a5926185817a33", "file": "media-upload.spec.ts", "line": 384, "column": 7}, {"title": "should handle unsupported file types", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-f9994ce3e7fb96f47df2", "file": "media-upload.spec.ts", "line": 393, "column": 7}, {"title": "should handle file size validation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-3b9a3e25c8d5a1db2e65", "file": "media-upload.spec.ts", "line": 384, "column": 7}, {"title": "should handle unsupported file types", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-3416f0a59089b352468e", "file": "media-upload.spec.ts", "line": 393, "column": 7}, {"title": "should handle file size validation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-8745f1e2447b161c7689", "file": "media-upload.spec.ts", "line": 384, "column": 7}, {"title": "should handle unsupported file types", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "101b3c7918c4b3dc4348-0d7aa392ef5887124abe", "file": "media-upload.spec.ts", "line": 393, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-08-31T14:49:48.463Z", "duration": 199501.125, "expected": 0, "skipped": 44, "unexpected": 33, "flaky": 0}}