{"config": {"configFile": "D:\\AI PRojects\\ChatApplication\\playwright.config.ts", "rootDir": "D:/AI PRojects/ChatApplication/e2e/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "e2e/reports/html"}], ["json", {"outputFile": "e2e/reports/results.json"}], ["junit", {"outputFile": "e2e/reports/results.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "firefox", "name": "firefox", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "webkit", "name": "webkit", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/AI PRojects/ChatApplication/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "D:/AI PRojects/ChatApplication/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.55.0", "workers": 6, "webServer": null}, "suites": [{"title": "messaging-test.spec.ts", "file": "messaging-test.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Messaging Test", "file": "messaging-test.spec.ts", "line": 33, "column": 6, "specs": [{"title": "should send and receive messages", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 4907, "errors": [], "stdout": [{"text": "Starting messaging test\n"}, {"text": "✅ Logged in successfully\n"}, {"text": "✅ Chat created with <PERSON>\n"}, {"text": "✅ Message sent\n"}, {"text": "✅ Message appears in chat\n"}, {"text": "✅ Message is visible and correct\n"}, {"text": "✅ Conversation list is visible\n"}, {"text": "✅ Second message sent\n"}, {"text": "✅ Second message appears in chat\n"}, {"text": "Total messages visible: 2\n"}, {"text": "✅ Multiple messages are visible\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-05T13:48:43.140Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "cf2dd4314621ad9ce662-de26213bbbdfc09b66cc", "file": "messaging-test.spec.ts", "line": 34, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-09-05T13:48:42.456Z", "duration": 6135.894, "expected": 1, "skipped": 0, "unexpected": 0, "flaky": 0}}