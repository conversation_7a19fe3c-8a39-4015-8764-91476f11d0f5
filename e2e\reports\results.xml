<testsuites id="" name="" tests="1" failures="0" skipped="0" errors="0" time="6.135894">
<testsuite name="messaging-test.spec.ts" timestamp="2025-09-05T13:48:42.490Z" hostname="chromium" tests="1" failures="0" skipped="0" time="4.907" errors="0">
<testcase name="Messaging Test › should send and receive messages" classname="messaging-test.spec.ts" time="4.907">
<system-out>
<![CDATA[Starting messaging test
✅ Logged in successfully
✅ Chat created with Harry
✅ Message sent
✅ Message appears in chat
✅ Message is visible and correct
✅ Conversation list is visible
✅ Second message sent
✅ Second message appears in chat
Total messages visible: 2
✅ Multiple messages are visible
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>