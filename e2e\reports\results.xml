<testsuites id="" name="" tests="77" failures="33" skipped="44" errors="0" time="199.501125">
<testsuite name="media-upload.spec.ts" timestamp="2025-08-31T14:49:48.625Z" hostname="chromium" tests="11" failures="11" skipped="0" time="327.634" errors="0">
<testcase name="Media Upload Functionality › should display media upload button" classname="media-upload.spec.ts" time="36.317">
<failure message="media-upload.spec.ts:99:7 should display media upload button" type="FAILURE">
<![CDATA[  [chromium] › media-upload.spec.ts:99:7 › Media Upload Functionality › should display media upload button 

    TimeoutError: page.waitForURL: Timeout 10000ms exceeded.
    =========================== logs ===========================
    waiting for navigation to "**/dashboard" until "load"
    ============================================================

      27 |   
      28 |   // Wait for redirect to dashboard
    > 29 |   await page.waitForURL('**/dashboard', { timeout: 10000 });
         |              ^
      30 |   console.log('✅ User logged in successfully');
      31 | }
      32 |
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:29:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:100:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--fb406-display-media-upload-button-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--fb406-display-media-upload-button-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--fb406-display-media-upload-button-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--fb406-display-media-upload-button-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should open upload dialog when plus button is clicked" classname="media-upload.spec.ts" time="37.225">
<failure message="media-upload.spec.ts:114:7 should open upload dialog when plus button is clicked" type="FAILURE">
<![CDATA[  [chromium] › media-upload.spec.ts:114:7 › Media Upload Functionality › should open upload dialog when plus button is clicked 

    Test timeout of 30000ms exceeded.

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:116:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-chromium\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should handle image file selection and show preview" classname="media-upload.spec.ts" time="37.974">
<failure message="media-upload.spec.ts:135:7 should handle image file selection and show preview" type="FAILURE">
<![CDATA[  [chromium] › media-upload.spec.ts:135:7 › Media Upload Functionality › should handle image file selection and show preview 

    Test timeout of 30000ms exceeded.

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:137:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--99ddf--selection-and-show-preview-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--99ddf--selection-and-show-preview-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--99ddf--selection-and-show-preview-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--99ddf--selection-and-show-preview-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--99ddf--selection-and-show-preview-chromium\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--99ddf--selection-and-show-preview-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should handle document file selection" classname="media-upload.spec.ts" time="35.388">
<failure message="media-upload.spec.ts:178:7 should handle document file selection" type="FAILURE">
<![CDATA[  [chromium] › media-upload.spec.ts:178:7 › Media Upload Functionality › should handle document file selection 

    Test timeout of 30000ms exceeded.

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:180:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--8f645-dle-document-file-selection-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--8f645-dle-document-file-selection-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--8f645-dle-document-file-selection-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--8f645-dle-document-file-selection-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--8f645-dle-document-file-selection-chromium\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--8f645-dle-document-file-selection-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should handle multiple file selection" classname="media-upload.spec.ts" time="35.993">
<failure message="media-upload.spec.ts:218:7 should handle multiple file selection" type="FAILURE">
<![CDATA[  [chromium] › media-upload.spec.ts:218:7 › Media Upload Functionality › should handle multiple file selection 

    Test timeout of 30000ms exceeded.

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:220:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-chromium\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should handle file removal from preview" classname="media-upload.spec.ts" time="34.545">
<failure message="media-upload.spec.ts:260:7 should handle file removal from preview" type="FAILURE">
<![CDATA[  [chromium] › media-upload.spec.ts:260:7 › Media Upload Functionality › should handle file removal from preview 

    TimeoutError: page.waitForURL: Timeout 10000ms exceeded.
    =========================== logs ===========================
    waiting for navigation to "**/dashboard" until "load"
    ============================================================

      27 |   
      28 |   // Wait for redirect to dashboard
    > 29 |   await page.waitForURL('**/dashboard', { timeout: 10000 });
         |              ^
      30 |   console.log('✅ User logged in successfully');
      31 | }
      32 |
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:29:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:261:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--c90be-e-file-removal-from-preview-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--c90be-e-file-removal-from-preview-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--c90be-e-file-removal-from-preview-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--c90be-e-file-removal-from-preview-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should simulate successful file upload" classname="media-upload.spec.ts" time="21.97">
<failure message="media-upload.spec.ts:293:7 should simulate successful file upload" type="FAILURE">
<![CDATA[  [chromium] › media-upload.spec.ts:293:7 › Media Upload Functionality › should simulate successful file upload 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:295:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--65765-late-successful-file-upload-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--65765-late-successful-file-upload-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--65765-late-successful-file-upload-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--65765-late-successful-file-upload-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--65765-late-successful-file-upload-chromium\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--65765-late-successful-file-upload-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should handle upload cancellation" classname="media-upload.spec.ts" time="22.153">
<failure message="media-upload.spec.ts:332:7 should handle upload cancellation" type="FAILURE">
<![CDATA[  [chromium] › media-upload.spec.ts:332:7 › Media Upload Functionality › should handle upload cancellation 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:334:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-chromium\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should be disabled when user is disconnected" classname="media-upload.spec.ts" time="18.794">
<failure message="media-upload.spec.ts:365:7 should be disabled when user is disconnected" type="FAILURE">
<![CDATA[  [chromium] › media-upload.spec.ts:365:7 › Media Upload Functionality › should be disabled when user is disconnected 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:367:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-chromium\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Error Scenarios › should handle file size validation" classname="media-upload.spec.ts" time="20.214">
<failure message="media-upload.spec.ts:384:7 should handle file size validation" type="FAILURE">
<![CDATA[  [chromium] › media-upload.spec.ts:384:7 › Media Upload Error Scenarios › should handle file size validation 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:386:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--e2983-handle-file-size-validation-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--e2983-handle-file-size-validation-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--e2983-handle-file-size-validation-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--e2983-handle-file-size-validation-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--e2983-handle-file-size-validation-chromium\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--e2983-handle-file-size-validation-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Error Scenarios › should handle unsupported file types" classname="media-upload.spec.ts" time="27.061">
<failure message="media-upload.spec.ts:393:7 should handle unsupported file types" type="FAILURE">
<![CDATA[  [chromium] › media-upload.spec.ts:393:7 › Media Upload Error Scenarios › should handle unsupported file types 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:395:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-chromium\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-chromium\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="media-upload.spec.ts" timestamp="2025-08-31T14:49:48.625Z" hostname="firefox" tests="11" failures="11" skipped="0" time="329.273" errors="0">
<testcase name="Media Upload Functionality › should display media upload button" classname="media-upload.spec.ts" time="31.373">
<failure message="media-upload.spec.ts:99:7 should display media upload button" type="FAILURE">
<![CDATA[  [firefox] › media-upload.spec.ts:99:7 › Media Upload Functionality › should display media upload button 

    Test timeout of 30000ms exceeded.

    Error: page.waitForLoadState: Test timeout of 30000ms exceeded.

      17 |   
      18 |   await page.goto(`${BASE_URL}/login`);
    > 19 |   await page.waitForLoadState('networkidle');
         |              ^
      20 |   
      21 |   // Fill login form
      22 |   await page.fill('input[type="email"]', TEST_USER.email);
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:19:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:100:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--fb406-display-media-upload-button-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--fb406-display-media-upload-button-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--fb406-display-media-upload-button-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--fb406-display-media-upload-button-firefox\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--fb406-display-media-upload-button-firefox\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--fb406-display-media-upload-button-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should open upload dialog when plus button is clicked" classname="media-upload.spec.ts" time="30.33">
<failure message="media-upload.spec.ts:114:7 should open upload dialog when plus button is clicked" type="FAILURE">
<![CDATA[  [firefox] › media-upload.spec.ts:114:7 › Media Upload Functionality › should open upload dialog when plus button is clicked 

    Test timeout of 30000ms exceeded.

    Error: page.waitForLoadState: Test timeout of 30000ms exceeded.

      17 |   
      18 |   await page.goto(`${BASE_URL}/login`);
    > 19 |   await page.waitForLoadState('networkidle');
         |              ^
      20 |   
      21 |   // Fill login form
      22 |   await page.fill('input[type="email"]', TEST_USER.email);
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:19:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:115:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-firefox\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-firefox\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should handle image file selection and show preview" classname="media-upload.spec.ts" time="31.56">
<failure message="media-upload.spec.ts:135:7 should handle image file selection and show preview" type="FAILURE">
<![CDATA[  [firefox] › media-upload.spec.ts:135:7 › Media Upload Functionality › should handle image file selection and show preview 

    Test timeout of 30000ms exceeded.

    Error: page.waitForLoadState: Test timeout of 30000ms exceeded.

      17 |   
      18 |   await page.goto(`${BASE_URL}/login`);
    > 19 |   await page.waitForLoadState('networkidle');
         |              ^
      20 |   
      21 |   // Fill login form
      22 |   await page.fill('input[type="email"]', TEST_USER.email);
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:19:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:136:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--99ddf--selection-and-show-preview-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--99ddf--selection-and-show-preview-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--99ddf--selection-and-show-preview-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--99ddf--selection-and-show-preview-firefox\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--99ddf--selection-and-show-preview-firefox\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--99ddf--selection-and-show-preview-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should handle document file selection" classname="media-upload.spec.ts" time="30.378">
<failure message="media-upload.spec.ts:178:7 should handle document file selection" type="FAILURE">
<![CDATA[  [firefox] › media-upload.spec.ts:178:7 › Media Upload Functionality › should handle document file selection 

    Test timeout of 30000ms exceeded.

    Error: page.waitForLoadState: Test timeout of 30000ms exceeded.

      17 |   
      18 |   await page.goto(`${BASE_URL}/login`);
    > 19 |   await page.waitForLoadState('networkidle');
         |              ^
      20 |   
      21 |   // Fill login form
      22 |   await page.fill('input[type="email"]', TEST_USER.email);
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:19:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:179:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--8f645-dle-document-file-selection-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--8f645-dle-document-file-selection-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--8f645-dle-document-file-selection-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--8f645-dle-document-file-selection-firefox\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--8f645-dle-document-file-selection-firefox\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--8f645-dle-document-file-selection-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should handle multiple file selection" classname="media-upload.spec.ts" time="53.438">
<failure message="media-upload.spec.ts:218:7 should handle multiple file selection" type="FAILURE">
<![CDATA[  [firefox] › media-upload.spec.ts:218:7 › Media Upload Functionality › should handle multiple file selection 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:220:5

    Tearing down "context" exceeded the test timeout of 30000ms.

    Error: browserContext.close: Test ended.
    Browser logs:

    <launching> C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1490\firefox\firefox.exe -no-remote -wait-for-browser -foreground -profile C:\Users\<USER>\AppData\Local\Temp\playwright_firefoxdev_profile-OgE1jy -juggler-pipe -silent
    <launched> pid=23188
    [pid=23188][err] JavaScript warning: resource://services-settings/Utils.sys.mjs, line 116: unreachable code after return statement
    [pid=23188][out] console.warn: services.settings: Ignoring preference override of remote settings server
    [pid=23188][out] console.warn: services.settings: Allow by setting MOZ_REMOTE_SETTINGS_DEVTOOLS=1 in the environment
    [pid=23188][out] 
    [pid=23188][out] Juggler listening to the pipe
    [pid=23188][out] console.error: "Warning: unrecognized command line flag" "-foreground"
    [pid=23188][out] Crash Annotation GraphicsCriticalError: |[G0][GFX1-]: Calling WaitFlushedEvent::Run: is delayed: 2161 (t=51.295) [GFX1-]: Calling WaitFlushedEvent::Run: is delayed: 2161
    [pid=23188][out] console.error: services.settings: 
    [pid=23188][out]   Message: EmptyDatabaseError: "main/nimbus-desktop-experiments" has not been synced yet
    [pid=23188][out]   Stack:
    [pid=23188][out]     EmptyDatabaseError@resource://services-settings/Database.sys.mjs:19:5
    [pid=23188][out] list@resource://services-settings/Database.sys.mjs:96:13
    [pid=23188][out] 
    [pid=23188][out] console.error: services.settings: 
    [pid=23188][out]   Message: EmptyDatabaseError: "main/nimbus-secure-experiments" has not been synced yet
    [pid=23188][out]   Stack:
    [pid=23188][out]     EmptyDatabaseError@resource://services-settings/Database.sys.mjs:19:5
    [pid=23188][out] list@resource://services-settings/Database.sys.mjs:96:13
    [pid=23188][out] 
    [pid=23188][err] JavaScript error: chrome://juggler/content/Helper.js, line 82: NS_ERROR_FAILURE: Component returned failure code: 0x80004005 (NS_ERROR_FAILURE) [nsIWebProgress.removeProgressListener]
    [pid=23188][out] console.warn: services.settings: #fetchAttachment: Forcing fallbackToDump to false due to Utils.LOAD_DUMPS being false
    [pid=23188][out] console.error: (new NotFoundError("Could not find fa0fc42c-d91d-fca7-34eb-806ff46062dc in cache or dump", "resource://services-settings/Attachments.sys.mjs", 48))
    [pid=23188][out] console.warn: "Unable to find the attachment for" "fa0fc42c-d91d-fca7-34eb-806ff46062dc"
    [pid=23188][out] console.warn: LoginRecipes: "Falling back to a synchronous message for: http://localhost:5002."
    [pid=23188] <gracefully close start>
    [pid=23188] <kill>
    [pid=23188] <will force kill>
    [pid=23188] taskkill stdout: SUCCESS: The process with PID 12212 (child process of PID 13264) has been terminated.
    SUCCESS: The process with PID 23112 (child process of PID 13264) has been terminated.
    SUCCESS: The process with PID 2304 (child process of PID 13264) has been terminated.
    SUCCESS: The process with PID 16188 (child process of PID 13264) has been terminated.
    SUCCESS: The process with PID 10052 (child process of PID 13264) has been terminated.
    SUCCESS: The process with PID 24312 (child process of PID 13264) has been terminated.
    SUCCESS: The process with PID 13264 (child process of PID 23188) has been terminated.
    SUCCESS: The process with PID 23188 (child process of PID 7904) has been terminated.


    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-firefox\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should handle file removal from preview" classname="media-upload.spec.ts" time="22.037">
<failure message="media-upload.spec.ts:260:7 should handle file removal from preview" type="FAILURE">
<![CDATA[  [firefox] › media-upload.spec.ts:260:7 › Media Upload Functionality › should handle file removal from preview 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:262:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--c90be-e-file-removal-from-preview-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--c90be-e-file-removal-from-preview-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--c90be-e-file-removal-from-preview-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--c90be-e-file-removal-from-preview-firefox\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--c90be-e-file-removal-from-preview-firefox\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--c90be-e-file-removal-from-preview-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should simulate successful file upload" classname="media-upload.spec.ts" time="30.316">
<failure message="media-upload.spec.ts:293:7 should simulate successful file upload" type="FAILURE">
<![CDATA[  [firefox] › media-upload.spec.ts:293:7 › Media Upload Functionality › should simulate successful file upload 

    Test timeout of 30000ms exceeded.

    Error: page.waitForLoadState: Test timeout of 30000ms exceeded.

      17 |   
      18 |   await page.goto(`${BASE_URL}/login`);
    > 19 |   await page.waitForLoadState('networkidle');
         |              ^
      20 |   
      21 |   // Fill login form
      22 |   await page.fill('input[type="email"]', TEST_USER.email);
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:19:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:294:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--65765-late-successful-file-upload-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--65765-late-successful-file-upload-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--65765-late-successful-file-upload-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--65765-late-successful-file-upload-firefox\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--65765-late-successful-file-upload-firefox\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--65765-late-successful-file-upload-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should handle upload cancellation" classname="media-upload.spec.ts" time="30.447">
<failure message="media-upload.spec.ts:332:7 should handle upload cancellation" type="FAILURE">
<![CDATA[  [firefox] › media-upload.spec.ts:332:7 › Media Upload Functionality › should handle upload cancellation 

    Test timeout of 30000ms exceeded.

    Error: page.waitForLoadState: Test timeout of 30000ms exceeded.

      17 |   
      18 |   await page.goto(`${BASE_URL}/login`);
    > 19 |   await page.waitForLoadState('networkidle');
         |              ^
      20 |   
      21 |   // Fill login form
      22 |   await page.fill('input[type="email"]', TEST_USER.email);
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:19:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:333:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-firefox\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-firefox\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should be disabled when user is disconnected" classname="media-upload.spec.ts" time="30.23">
<failure message="media-upload.spec.ts:365:7 should be disabled when user is disconnected" type="FAILURE">
<![CDATA[  [firefox] › media-upload.spec.ts:365:7 › Media Upload Functionality › should be disabled when user is disconnected 

    Test timeout of 30000ms exceeded.

    Error: page.waitForLoadState: Test timeout of 30000ms exceeded.

      17 |   
      18 |   await page.goto(`${BASE_URL}/login`);
    > 19 |   await page.waitForLoadState('networkidle');
         |              ^
      20 |   
      21 |   // Fill login form
      22 |   await page.fill('input[type="email"]', TEST_USER.email);
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:19:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:366:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-firefox\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-firefox\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Error Scenarios › should handle file size validation" classname="media-upload.spec.ts" time="19.518">
<failure message="media-upload.spec.ts:384:7 should handle file size validation" type="FAILURE">
<![CDATA[  [firefox] › media-upload.spec.ts:384:7 › Media Upload Error Scenarios › should handle file size validation 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button[type="submit"]')
        - locator resolved to <button type="submit" data-testid="login-button" class="inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 px-4 py-2 text-sm  w-full">Sign in</button>
      - attempting click action
        - waiting for element to be visible, enabled and stable


      24 |   
      25 |   // Click login button
    > 26 |   await page.click('button[type="submit"]');
         |              ^
      27 |   
      28 |   // Wait for redirect to dashboard
      29 |   await page.waitForURL('**/dashboard', { timeout: 10000 });
        at loginUser (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:26:14)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:385:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--e2983-handle-file-size-validation-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--e2983-handle-file-size-validation-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--e2983-handle-file-size-validation-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--e2983-handle-file-size-validation-firefox\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--e2983-handle-file-size-validation-firefox\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--e2983-handle-file-size-validation-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Error Scenarios › should handle unsupported file types" classname="media-upload.spec.ts" time="19.646">
<failure message="media-upload.spec.ts:393:7 should handle unsupported file types" type="FAILURE">
<![CDATA[  [firefox] › media-upload.spec.ts:393:7 › Media Upload Error Scenarios › should handle unsupported file types 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:395:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-firefox\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-firefox\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-firefox\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="media-upload.spec.ts" timestamp="2025-08-31T14:49:48.625Z" hostname="webkit" tests="11" failures="11" skipped="0" time="211.46" errors="0">
<testcase name="Media Upload Functionality › should display media upload button" classname="media-upload.spec.ts" time="18.825">
<failure message="media-upload.spec.ts:99:7 should display media upload button" type="FAILURE">
<![CDATA[  [webkit] › media-upload.spec.ts:99:7 › Media Upload Functionality › should display media upload button 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:101:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--fb406-display-media-upload-button-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--fb406-display-media-upload-button-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--fb406-display-media-upload-button-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--fb406-display-media-upload-button-webkit\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--fb406-display-media-upload-button-webkit\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--fb406-display-media-upload-button-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should open upload dialog when plus button is clicked" classname="media-upload.spec.ts" time="18.513">
<failure message="media-upload.spec.ts:114:7 should open upload dialog when plus button is clicked" type="FAILURE">
<![CDATA[  [webkit] › media-upload.spec.ts:114:7 › Media Upload Functionality › should open upload dialog when plus button is clicked 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:116:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-webkit\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-webkit\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--7d463-when-plus-button-is-clicked-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should handle image file selection and show preview" classname="media-upload.spec.ts" time="17.678">
<failure message="media-upload.spec.ts:135:7 should handle image file selection and show preview" type="FAILURE">
<![CDATA[  [webkit] › media-upload.spec.ts:135:7 › Media Upload Functionality › should handle image file selection and show preview 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:137:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--99ddf--selection-and-show-preview-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--99ddf--selection-and-show-preview-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--99ddf--selection-and-show-preview-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--99ddf--selection-and-show-preview-webkit\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--99ddf--selection-and-show-preview-webkit\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--99ddf--selection-and-show-preview-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should handle document file selection" classname="media-upload.spec.ts" time="18.788">
<failure message="media-upload.spec.ts:178:7 should handle document file selection" type="FAILURE">
<![CDATA[  [webkit] › media-upload.spec.ts:178:7 › Media Upload Functionality › should handle document file selection 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:180:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--8f645-dle-document-file-selection-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--8f645-dle-document-file-selection-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--8f645-dle-document-file-selection-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--8f645-dle-document-file-selection-webkit\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--8f645-dle-document-file-selection-webkit\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--8f645-dle-document-file-selection-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should handle multiple file selection" classname="media-upload.spec.ts" time="19.537">
<failure message="media-upload.spec.ts:218:7 should handle multiple file selection" type="FAILURE">
<![CDATA[  [webkit] › media-upload.spec.ts:218:7 › Media Upload Functionality › should handle multiple file selection 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:220:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-webkit\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-webkit\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--cc19b-dle-multiple-file-selection-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should handle file removal from preview" classname="media-upload.spec.ts" time="17.675">
<failure message="media-upload.spec.ts:260:7 should handle file removal from preview" type="FAILURE">
<![CDATA[  [webkit] › media-upload.spec.ts:260:7 › Media Upload Functionality › should handle file removal from preview 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:262:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--c90be-e-file-removal-from-preview-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--c90be-e-file-removal-from-preview-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--c90be-e-file-removal-from-preview-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--c90be-e-file-removal-from-preview-webkit\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--c90be-e-file-removal-from-preview-webkit\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--c90be-e-file-removal-from-preview-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should simulate successful file upload" classname="media-upload.spec.ts" time="18.716">
<failure message="media-upload.spec.ts:293:7 should simulate successful file upload" type="FAILURE">
<![CDATA[  [webkit] › media-upload.spec.ts:293:7 › Media Upload Functionality › should simulate successful file upload 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:295:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--65765-late-successful-file-upload-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--65765-late-successful-file-upload-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--65765-late-successful-file-upload-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--65765-late-successful-file-upload-webkit\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--65765-late-successful-file-upload-webkit\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--65765-late-successful-file-upload-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should handle upload cancellation" classname="media-upload.spec.ts" time="19.899">
<failure message="media-upload.spec.ts:332:7 should handle upload cancellation" type="FAILURE">
<![CDATA[  [webkit] › media-upload.spec.ts:332:7 › Media Upload Functionality › should handle upload cancellation 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:334:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-webkit\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-webkit\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--cd8bd--handle-upload-cancellation-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Functionality › should be disabled when user is disconnected" classname="media-upload.spec.ts" time="21.229">
<failure message="media-upload.spec.ts:365:7 should be disabled when user is disconnected" type="FAILURE">
<![CDATA[  [webkit] › media-upload.spec.ts:365:7 › Media Upload Functionality › should be disabled when user is disconnected 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:367:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-webkit\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-webkit\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--e2dea-d-when-user-is-disconnected-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Error Scenarios › should handle file size validation" classname="media-upload.spec.ts" time="19.45">
<failure message="media-upload.spec.ts:384:7 should handle file size validation" type="FAILURE">
<![CDATA[  [webkit] › media-upload.spec.ts:384:7 › Media Upload Error Scenarios › should handle file size validation 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:386:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--e2983-handle-file-size-validation-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--e2983-handle-file-size-validation-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--e2983-handle-file-size-validation-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--e2983-handle-file-size-validation-webkit\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--e2983-handle-file-size-validation-webkit\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--e2983-handle-file-size-validation-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Media Upload Error Scenarios › should handle unsupported file types" classname="media-upload.spec.ts" time="21.15">
<failure message="media-upload.spec.ts:393:7 should handle unsupported file types" type="FAILURE">
<![CDATA[  [webkit] › media-upload.spec.ts:393:7 › Media Upload Error Scenarios › should handle unsupported file types 

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-conversation-button"]')


      47 |   } else {
      48 |     // Create a new conversation if none exists
    > 49 |     await page.click('[data-testid="new-conversation-button"]');
         |                ^
      50 |     await page.fill('[data-testid="user-search-input"]', '<EMAIL>');
      51 |     await page.waitForSelector('[data-testid="user-search-result"]');
      52 |     await page.click('[data-testid="user-search-result"]');
        at navigateToChat (D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:49:16)
        at D:\AI PRojects\ChatApplication\e2e\tests\media-upload.spec.ts:395:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Logging in user...
✅ User logged in successfully
💬 Navigating to chat conversation...

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-webkit\test-failed-1.png]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-webkit\video.webm]]

[[ATTACHMENT|..\test-results\media-upload-Media-Upload--73a1e-ndle-unsupported-file-types-webkit\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="media-upload.spec.ts" timestamp="2025-08-31T14:49:48.625Z" hostname="Mobile Chrome" tests="11" failures="0" skipped="11" time="32.971" errors="0">
<testcase name="Media Upload Functionality › should display media upload button" classname="media-upload.spec.ts" time="9.404">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should open upload dialog when plus button is clicked" classname="media-upload.spec.ts" time="8.197">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle image file selection and show preview" classname="media-upload.spec.ts" time="7.228">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle document file selection" classname="media-upload.spec.ts" time="6.965">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle multiple file selection" classname="media-upload.spec.ts" time="1.064">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle file removal from preview" classname="media-upload.spec.ts" time="0.113">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should simulate successful file upload" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle upload cancellation" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should be disabled when user is disconnected" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Error Scenarios › should handle file size validation" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Error Scenarios › should handle unsupported file types" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="media-upload.spec.ts" timestamp="2025-08-31T14:49:48.625Z" hostname="Mobile Safari" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Media Upload Functionality › should display media upload button" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should open upload dialog when plus button is clicked" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle image file selection and show preview" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle document file selection" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle multiple file selection" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle file removal from preview" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should simulate successful file upload" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle upload cancellation" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should be disabled when user is disconnected" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Error Scenarios › should handle file size validation" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Error Scenarios › should handle unsupported file types" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="media-upload.spec.ts" timestamp="2025-08-31T14:49:48.625Z" hostname="Microsoft Edge" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Media Upload Functionality › should display media upload button" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should open upload dialog when plus button is clicked" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle image file selection and show preview" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle document file selection" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle multiple file selection" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle file removal from preview" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should simulate successful file upload" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle upload cancellation" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should be disabled when user is disconnected" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Error Scenarios › should handle file size validation" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Error Scenarios › should handle unsupported file types" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="media-upload.spec.ts" timestamp="2025-08-31T14:49:48.625Z" hostname="Google Chrome" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Media Upload Functionality › should display media upload button" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should open upload dialog when plus button is clicked" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle image file selection and show preview" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle document file selection" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle multiple file selection" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle file removal from preview" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should simulate successful file upload" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should handle upload cancellation" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Functionality › should be disabled when user is disconnected" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Error Scenarios › should handle file size validation" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Media Upload Error Scenarios › should handle unsupported file types" classname="media-upload.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>