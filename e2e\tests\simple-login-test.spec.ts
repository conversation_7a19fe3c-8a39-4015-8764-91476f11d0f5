// e2e/tests/simple-login-test.spec.ts
import { test, expect, Page } from '@playwright/test';

const FRONTEND_URL = 'http://localhost:5001';

test.describe('Simple Login Test', () => {
  test('should login successfully', async ({ page }) => {
    console.log('Starting simple login test');
    
    // Navigate to frontend
    await page.goto(FRONTEND_URL);
    console.log('Navigated to frontend');
    
    // Wait for page to load
    await page.waitForSelector('form', { timeout: 10000 });
    console.log('Form found');
    
    // Fill login form
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'testpass123');
    console.log('Form filled');
    
    // Submit login
    await page.click('[data-testid="login-button"]');
    console.log('Login button clicked');
    
    // Wait for dashboard or check for errors
    try {
      await page.waitForSelector('[data-testid="dashboard-header"]', { timeout: 15000 });
      console.log('✅ Login successful - dashboard visible');
    } catch (error) {
      // Check for error messages
      const errorElement = await page.locator('[data-testid="error-message"]').first();
      if (await errorElement.isVisible()) {
        const errorText = await errorElement.textContent();
        console.log('❌ Login error:', errorText);
      } else {
        console.log('❌ Login failed - no dashboard or error message found');
      }
      throw error;
    }
  });
});
