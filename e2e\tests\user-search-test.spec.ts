// e2e/tests/user-search-test.spec.ts
import { test, expect, Page } from '@playwright/test';

const FRONTEND_URL = 'http://localhost:5001';

async function loginUser(page: Page) {
  await page.goto(FRONTEND_URL);
  await page.waitForSelector('form', { timeout: 10000 });
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'testpass123');
  await page.click('[data-testid="login-button"]');
  await page.waitForSelector('[data-testid="dashboard-header"]', { timeout: 15000 });
}

test.describe('User Search Test', () => {
  test('should search for users and create chat', async ({ page }) => {
    console.log('Starting user search test');
    
    // Login
    await loginUser(page);
    console.log('✅ Logged in successfully');
    
    // Click new chat button
    await page.click('[data-testid="new-chat-button"]');
    console.log('✅ New chat button clicked');
    
    // Wait for user search modal
    await page.waitForSelector('[data-testid="user-search-modal"]', { timeout: 10000 });
    console.log('✅ User search modal opened');
    
    // Search for harry
    await page.fill('[data-testid="user-search-input"]', 'harry');
    console.log('✅ Search query entered');
    
    // Wait for search results
    await page.waitForTimeout(2000); // Wait for debounced search
    
    // Check if user results appear
    const userResults = await page.locator('[data-testid="user-result"]').count();
    console.log(`Found ${userResults} user results`);
    
    if (userResults > 0) {
      // Click on the first user's action button
      await page.click('[data-testid="user-action-button"]');
      console.log('✅ User action button clicked');
      
      // Wait for chat area to be visible
      await page.waitForSelector('[data-testid="chat-area"]', { timeout: 10000 });
      console.log('✅ Chat area is visible');
      
      // Check if message input is available
      const messageInput = await page.locator('[data-testid="message-input"]');
      await expect(messageInput).toBeVisible();
      console.log('✅ Message input is visible');
      
    } else {
      console.log('❌ No user results found');
    }
  });
});
